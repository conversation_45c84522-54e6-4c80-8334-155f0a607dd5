package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.ItemCompositionSaveReq;
import com.struts.etims.execute.model.ItemCompositionSaveReqBody;
import com.struts.etims.execute.model.ItemCompositionSaveRes;
import com.struts.etims.execute.model.ItemReq;
import com.struts.etims.execute.model.ItemReqBody;
import com.struts.etims.execute.model.ItemRes;
import com.struts.etims.execute.model.ItemSaveReq;
import com.struts.etims.execute.model.ItemSaveReqBody;
import com.struts.etims.execute.model.ItemSaveRes;
import com.struts.etims.manage.DataResendManager;

@RestController
@RequestMapping({ "/items" })
public class ItemExcute {

    @Autowired
    ApiClient apiClient;

    @Autowired
    DataResendManager resendMng;

    @PostMapping({ "/selectItems" })
    public ItemRes selectItemList(@RequestBody ItemReq req) {
        String resStr = "";
        ItemRes res = new ItemRes();

        String tinBhfPath = req.getTin() + "_" + req.getBhfId();
        // System.out.println("tinBhfPath=" + tinBhfPath);

        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_ITEM_SEARCH, "selectItemList",
                ItemReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            // System.out.println("resStr:: " + resStr);
            res = (ItemRes) VsdcUtil.jsonToObject(resStr, ItemRes.class);
        } catch (Exception e) {
            // System.out.println("encountered an err: " + e.getMessage());
            res = new ItemRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/saveItems" })
    public ItemSaveRes saveItem(@RequestBody ItemSaveReq req) {
        String resStr = "";
        ItemSaveRes res = new ItemSaveRes();

        String tinBhfPath = req.getTin() + "_" + req.getBhfId();
        // System.out.println("tinBhfPath=" + tinBhfPath);

        // DataResendManager resendMng = new DataResendManager();
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_ITEM_SAVE, "saveItem",
                ItemSaveReqBody.class);
        try {
            resStr = apiClient.getClient(clientArg, req);

            // System.out.println("resStr: " + resStr);

            res = (ItemSaveRes) VsdcUtil.jsonToObject(resStr, ItemSaveRes.class);

            String reSendId = "saveItem_" + VsdcUtil.getDate("yyyyMMddHHmmss");

            // System.out.println("save item resStr: " + resStr);

            if ("896".equals(res.getResultCd()) || "894".equals(res.getResultCd())) {
                String reqJson = VsdcUtil.objectToJson(req);
                resendMng.saveReSendFile("saveItem", reSendId, reqJson, tinBhfPath);
            }
        } catch (Exception e) {

            System.out.println("save item error: " + e.getMessage());

            res = new ItemSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/saveItemComposition" })
    public ItemCompositionSaveRes saveItemComposition(@RequestBody ItemCompositionSaveReq req) {
        String resStr = null;
        ItemCompositionSaveRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_ITEM_COMPOSITION_SAVE, "saveItemComposition",
                ItemCompositionSaveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (ItemCompositionSaveRes) VsdcUtil.jsonToObject(resStr, ItemCompositionSaveRes.class);
        } catch (Exception e) {
            res = new ItemCompositionSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
