package com.struts.etims.model;

import java.math.BigDecimal;

public class ZreportDailyReceipt {

    private String tin;

    private String bhfId;

    private Long invcNo;

    private String rptNo;

    private Long rcptNo;

    private String rcptTyCd;

    private String salesTyCd;

    private BigDecimal totTaxAmt;

    private BigDecimal totTaxblAmt;

    public String getTin() {
        return this.tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getBhfId() {
        return this.bhfId;
    }

    public void setBhfId(String bhfId) {
        this.bhfId = bhfId;
    }

    public Long getInvcNo() {
        return this.invcNo;
    }

    public void setInvcNo(Long invcNo) {
        this.invcNo = invcNo;
    }

    public String getRptNo() {
        return this.rptNo;
    }

    public void setRptNo(String rptNo) {
        this.rptNo = rptNo;
    }

    public Long getRcptNo() {
        return this.rcptNo;
    }

    public void setRcptNo(Long rcptNo) {
        this.rcptNo = rcptNo;
    }

    public String getSalesTyCd() {
        return this.salesTyCd;
    }

    public void setSalesTyCd(String salesTyCd) {
        this.salesTyCd = salesTyCd;
    }

    public String getRcptTyCd() {
        return this.rcptTyCd;
    }

    public void setRcptTyCd(String rcptTyCd) {
        this.rcptTyCd = rcptTyCd;
    }

    public BigDecimal getTotTaxAmt() {
        return this.totTaxAmt;
    }

    public void setTotTaxAmt(BigDecimal totTaxAmt) {
        this.totTaxAmt = totTaxAmt;
    }

    public BigDecimal getTotTaxblAmt() {
        return this.totTaxblAmt;
    }

    public void setTotTaxblAmt(BigDecimal totTaxblAmt) {
        this.totTaxblAmt = totTaxblAmt;
    }
}
