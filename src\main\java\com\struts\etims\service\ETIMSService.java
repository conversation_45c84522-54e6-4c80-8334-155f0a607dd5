package com.struts.etims.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.entity.EtimsSaleItem;
import com.struts.etims.entity.EtimsSaveSaleReceipt;
import com.struts.etims.entity.EtimsSaveSaleTransaction;
import com.struts.etims.execute.TrnsSalesExcute;
import com.struts.etims.execute.model.TrnsSalesSaveWrItem;
import com.struts.etims.execute.model.TrnsSalesSaveWrReceipt;
import com.struts.etims.execute.model.TrnsSalesSaveWrReq;
import com.struts.etims.execute.model.TrnsSalesSaveWrReqBody;
import com.struts.etims.execute.model.TrnsSalesSaveWrRes;
import com.struts.etims.execute.model.TrnsSalesSaveWrResData;
import com.struts.etims.execute.offline.CheckApiConnection;
import com.struts.etims.manage.DataResendManager;
import com.struts.etims.manage.DataZreportManager;
import com.struts.etims.manage.SequenceManager;
import com.struts.etims.model.ZreportDailyReceipt;
import com.struts.etims.repo.EtimsSaleItemRepository;
import com.struts.etims.repo.EtimsSaveSaleReceiptRepository;
import com.struts.etims.repo.EtimsSaveSaleTransactionRepository;

@Service
public class ETIMSService {
    private static final Logger logger = LoggerFactory.getLogger(ETIMSService.class);

    @Autowired
    ApiClient apiClient;

    @Autowired
    CheckApiConnection checkConn;

    @Autowired
    private EtimsSaleItemRepository etimsSaleItemRepository;

    @Autowired
    private EtimsSaveSaleTransactionRepository etimsSaveSaleTransactionRepo;

    @Autowired
    private EtimsSaveSaleReceiptRepository etimsSaveSaleReceiptRepo;

    @Autowired
    DataResendManager resendMng;

    public EtimsSaveSaleTransaction saveReceiptDetailsToDB(TrnsSalesSaveWrReq req, TrnsSalesSaveWrResData resData,
            String invRcptKind) {
        EtimsSaveSaleTransaction trx = new EtimsSaveSaleTransaction();

        // Save sale transaction
        Optional<EtimsSaveSaleTransaction> existingTransaction = etimsSaveSaleTransactionRepo
                .findByTinAndInvcNoAndRcptTyCd(req.getTin(), req.getInvcNo(), req.getRcptTyCd());
        if (existingTransaction.isPresent()) {
            trx = existingTransaction.get();
        } else {

            trx.setTin(req.getTin());
            trx.setBhfId(req.getBhfId());
            trx.setCompanyPIN(req.getTin());
            trx.setInvcNo(req.getInvcNo());
            trx.setOrgInvcNo(req.getOrgInvcNo());
            trx.setCfmDt(req.getCfmDt());
            trx.setCustNm(req.getCustNm());
            trx.setCustTin(req.getCustTin());
            trx.setInvRcptKind(invRcptKind);
            trx.setModrId(req.getModrId());
            trx.setModrNm(req.getModrNm());
            trx.setPmtTyCd(req.getPmtTyCd());
            trx.setPrchrAcptcYn(req.getPrchrAcptcYn());
            trx.setRcptTyCd(req.getRcptTyCd());
            trx.setRegrId(req.getRegrId());
            trx.setRegrNm(req.getRegrNm());
            trx.setRemark(req.getRemark());
            trx.setTotAmt(req.getTotAmt());
            trx.setRfdDt(req.getRfdDt());
            trx.setRfdRsnCd(req.getRfdRsnCd());
            trx.setSalesDt(req.getSalesDt());
            trx.setStockRlsDt(req.getStockRlsDt());
            trx.setSalesSttsCd(req.getSalesSttsCd());
            trx.setSalesTyCd(req.getSalesTyCd());
            trx.setTaxAmtA(req.getTaxAmtA());
            trx.setTaxAmtB(req.getTaxAmtB());
            trx.setTaxAmtC(req.getTaxAmtC());
            trx.setTaxAmtD(req.getTaxAmtD());
            trx.setTaxAmtE(req.getTaxAmtE());
            trx.setTaxRtA(req.getTaxRtA());
            trx.setTaxRtB(req.getTaxRtB());
            trx.setTaxRtC(req.getTaxRtC());
            trx.setTaxRtD(req.getTaxRtD());
            trx.setTaxRtE(req.getTaxRtE());
            trx.setTaxblAmtA(req.getTaxblAmtA());
            trx.setTaxblAmtB(req.getTaxblAmtB());
            trx.setTaxblAmtC(req.getTaxblAmtC());
            trx.setTaxblAmtD(req.getTaxblAmtD());
            trx.setTaxblAmtE(req.getTaxblAmtE());
            trx.setTotItemCnt(req.getTotItemCnt());
            trx.setTotTaxAmt(req.getTotTaxAmt());
            trx.setTotTaxblAmt(req.getTotTaxblAmt());
            trx.setKraSendStatus("pending");

            // Set signature data
            trx.setIntrlData(req.getReceipt().getIntrlData());
            trx.setRcptSign(req.getReceipt().getRcptSign());
            trx.setResultDt(req.getReceipt().getRcptPbctDt());
            trx.setSdcId(resData.getSdcId());
            trx.setMrcNo(resData.getMrcNo());
            trx.setRcptNo(resData.getRcptNo());
            trx.setTotRcptNo(resData.getTotRcptNo());
            trx.setVsdcRcptPbctDate(resData.getVsdcRcptPbctDate());

            etimsSaveSaleTransactionRepo.save(trx);
        }

        // Save sale items
        List<EtimsSaleItem> saleItems = new ArrayList<EtimsSaleItem>();

        for (TrnsSalesSaveWrItem item : req.getItemList()) {
            EtimsSaleItem etimsItem = new EtimsSaleItem();
            etimsItem.setEtimsSaveSaleTransactionID(trx.getId());
            etimsItem.setItemSeq(item.getItemSeq());
            etimsItem.setItemCd(item.getItemCd());
            etimsItem.setItemClsCd(item.getItemClsCd());
            etimsItem.setItemNm(item.getItemNm());
            etimsItem.setBcd(item.getBcd());
            etimsItem.setPkgUnitCd(item.getPkgUnitCd());
            etimsItem.setPkg(item.getPkg());
            etimsItem.setQtyUnitCd(item.getQtyUnitCd());
            etimsItem.setQty(item.getQty());
            etimsItem.setPrc(item.getPrc());
            etimsItem.setSplyAmt(item.getSplyAmt());
            etimsItem.setDcRt(item.getDcRt());
            etimsItem.setDcAmt(item.getDcAmt());
            etimsItem.setIsrccCd(item.getIsrccCd());
            etimsItem.setIsrccNm(item.getIsrccNm());
            etimsItem.setIsrcRt(item.getIsrcRt());
            etimsItem.setIsrcAmt(item.getIsrcAmt());
            etimsItem.setTaxTyCd(item.getTaxTyCd());
            etimsItem.setTaxblAmt(item.getTaxblAmt());
            etimsItem.setTaxAmt(item.getTaxAmt());
            etimsItem.setTotAmt(item.getTotAmt());
            etimsItem.setCompanyPIN(trx.getCompanyPIN());

            saleItems.add(etimsItem);
        }

        etimsSaleItemRepository.saveAll(saleItems);

        // Save receipt details
        Optional<EtimsSaveSaleReceipt> existingReceipt = etimsSaveSaleReceiptRepo
                .findByEtimsSaveSaleTransactionID(trx.getId());
        if (existingReceipt.isPresent()) {
            logger.info("existingReceipt id: " + existingReceipt.get().getId());
            return trx;
        } else {
            // Save sale receipt
            EtimsSaveSaleReceipt saleReceipt = new EtimsSaveSaleReceipt();
            saleReceipt.setAdrs(req.getReceipt().getAdrs());
            saleReceipt.setBtmMsg(req.getReceipt().getBtmMsg());
            saleReceipt.setCurRcptNo(resData.getRcptNo());
            saleReceipt.setEtimsSaveSaleTransactionID(trx.getId());
            saleReceipt.setCurRcptNo(req.getReceipt().getCurRcptNo());
            saleReceipt.setCustMblNo(req.getReceipt().getCustMblNo());
            saleReceipt.setCustTin(req.getReceipt().getCustTin());
            saleReceipt.setIntrlData(req.getReceipt().getIntrlData());
            saleReceipt.setJrnl(req.getReceipt().getJrnl());
            saleReceipt.setPrchrAcptcYn(req.getReceipt().getPrchrAcptcYn());
            saleReceipt.setRcptSign(req.getReceipt().getRcptSign());
            saleReceipt.setRcptPbctDt(req.getReceipt().getRcptPbctDt());
            saleReceipt.setRptNo(resData.getRcptNo());
            saleReceipt.setTotRcptNo(resData.getRcptNo());
            saleReceipt.setTrdeNm(req.getReceipt().getTrdeNm());
            etimsSaveSaleReceiptRepo.save(saleReceipt);
        }

        return trx;
    }

    public void PostUnsentSalesTransactionsToKRA() {

        // Fetch all unsent sales transactions
        List<EtimsSaveSaleTransaction> pendingTransactions = etimsSaveSaleTransactionRepo
                .findAllUnsentSalesTransactions();

        if (pendingTransactions.isEmpty()) {
            return;
        }

        boolean reSend = false;

        for (EtimsSaveSaleTransaction transaction : pendingTransactions) {
            try {
                // Mark as processing to prevent duplicate processing
                etimsSaveSaleTransactionRepo.markAsProcessing(transaction.getId());

                logger.info("posting transaction to KRA, ID: " + transaction.getId());

                ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_TRNS_SALES_SAVE, "saveTrnsSalesVsdc",
                        TrnsSalesSaveWrReqBody.class);

                String tinBhfPath = transaction.getTin() + "_" + transaction.getBhfId();
                Long rcptTpNo = transaction.getRcptNo();
                Long rcptNo = transaction.getRcptNo();
                String invRcptKind = transaction.getInvRcptKind();

                Optional<EtimsSaveSaleReceipt> existingReceipt = etimsSaveSaleReceiptRepo
                        .findByEtimsSaveSaleTransactionID(transaction.getId());
                List<EtimsSaleItem> saleItems = etimsSaleItemRepository.findAllForSalesTransaction(transaction.getId());
                TrnsSalesSaveWrReq req = populateEtimsTrnsSalesObj(transaction, existingReceipt, saleItems);

                try {

                    // push data to KRA Server
                    String reSendId = transaction.getTin() + "_" + transaction.getBhfId() + "_"
                            + transaction.getInvcNo();
                    String resStr = this.apiClient.getClient(clientArg, req);
                    logger.info("apiClient resStr: " + resStr);

                    TrnsSalesSaveWrRes res = (TrnsSalesSaveWrRes) VsdcUtil.jsonToObject(resStr,
                            TrnsSalesSaveWrRes.class);
                    String resultCode = res.getResultCd();
                    logger.info("resultCode: " + resultCode);
                    logger.info("reSend: " + reSend);

                    if (resultCode.equals("910") || resultCode.equals("999") || resultCode.equals("901")) {

                        // 901 - Invalid Device
                        // 910 - Request parameter error
                        // 999 - Ask Administrator

                        // Failed due to itemList
                        transaction.setKraSendStatus("failed");
                        transaction.setKraResultCode(resultCode);

                        String errorMsg = res.getResultMsg();
                        String truncatedErrMsg = errorMsg.length() > 254 ? errorMsg.substring(0, 254) : errorMsg;
                        transaction.setKraResponseMessage(truncatedErrMsg);

                        etimsSaveSaleTransactionRepo.save(transaction);

                        ObjectMapper objectMapper = new ObjectMapper();
                        String jsonInputString = objectMapper.writeValueAsString(transaction);
                        logger.info("err json str=" + jsonInputString);

                        // ToDo: Send an email to admin with the invoice details
                        Thread.sleep(1000); // sleep for 1 second
                    }

                    if (!reSend)
                        if (resultCode.equals("896") || resultCode.equals("894")
                                || resultCode.equals("000")) {
                            SequenceManager.setRcptNo(rcptNo, tinBhfPath);
                            if ("serlInvNsr".equals(invRcptKind)) {
                                SequenceManager.setInvcNoNsr(rcptTpNo, tinBhfPath);
                            } else if ("serlInvTsr".equals(invRcptKind)) {
                                SequenceManager.setInvcNoTsr(rcptTpNo, tinBhfPath);
                            } else if ("serlInvPs".equals(invRcptKind)) {
                                SequenceManager.setInvcNoPs(rcptTpNo, tinBhfPath);
                            } else if ("serlInvCsr".equals(invRcptKind)) {
                                SequenceManager.setInvcNoCsr(rcptTpNo, tinBhfPath);
                            }
                        }

                    if (resultCode.equals("896") || resultCode.equals("894")
                            || resultCode.equals("000") || resultCode.equals("924")) {
                        logger.info("Data posted to KRA successfully.");

                        // After successful response from KRA, update the kra_send_status status to sent
                        // / success

                        transaction.setKraSendStatus("success");
                        transaction.setKraResultCode(resultCode);
                        transaction.setProcessingStatus(null);
                        transaction.setKraResponseMessage("Data posted to KRA successfully.");
                        etimsSaveSaleTransactionRepo.save(transaction);

                        // Add a ZReport entry
                        ZreportDailyReceipt receipt = new ZreportDailyReceipt();
                        receipt.setInvcNo(req.getInvcNo());
                        receipt.setRcptNo(transaction.getRcptNo());
                        receipt.setSalesTyCd(req.getSalesTyCd());
                        receipt.setRcptTyCd(req.getRcptTyCd());
                        receipt.setTotTaxblAmt(req.getTotTaxblAmt());
                        receipt.setTotTaxAmt(req.getTotTaxAmt());
                        DataZreportManager.saveZReportDailyFile(req.getTin(), req.getBhfId(),
                                res.getResultDt().substring(0, 8),
                                receipt, tinBhfPath);
                    } else {
                        logger.info("error posting data to KRA");
                    }

                    if (!reSend)
                        if ("896".equals(res.getResultCd()) || "894".equals(res.getResultCd())) {
                            String reqJson = VsdcUtil.objectToJson(req);
                            this.resendMng.saveReSendFile("trnsSales", reSendId, reqJson, tinBhfPath);
                        }

                    // Send other pending data
                    // String filePathString = DataResendManager.getDataReSendPath(tinBhfPath,
                    // "trnsSales");
                    // Path resendPath = Paths.get(filePathString, new String[0]);
                    // if (!VsdcUtil.isEmpty(resendPath))
                    // if (this.checkConn.isConnected()) {
                    // this.resendMng.loadReSendFile("trnsSales", tinBhfPath);
                    // this.resendMng.loadReSendStockFile("stockMaster", tinBhfPath);
                    // this.resendMng.loadReSendStockIOFile("stockIO", tinBhfPath);
                    // this.resendMng.loadReSendItemFile("saveItem", tinBhfPath);
                    // this.resendMng.loadReSendImportsFile("trnsImports", tinBhfPath);
                    // this.resendMng.loadReSendPurchaseFile("trnsPurchase", tinBhfPath);
                    // }

                    Thread.sleep(1000); // sleep for 1 second

                } catch (Exception e) {
                    // e.printStackTrace();
                    logger.info("error posting data to KRA, error=" + e.getMessage());
                    logger.info("-------------------------------------------------------------------------");
                }

            } catch (Exception e) {
                logger.info("error posting data to KRA, error=" + e.getMessage());
            } finally {
                // Clear processing status in case of success or failure
                etimsSaveSaleTransactionRepo.clearProcessingStatus(transaction.getId());
            }
        }
    }

    public void RetryFailedSalesTransactionsSendingToKRA() {
        // Calculate timestamp for 5 minutes ago
        // Calendar cal = Calendar.getInstance();
        // cal.add(Calendar.MINUTE, -5);
        // Date fiveMinutesAgo = cal.getTime();

        // Fetch all failed sales transactions
        List<EtimsSaveSaleTransaction> failedTransactions = etimsSaveSaleTransactionRepo
                .findAllFailedSalesTransactions();

        if (failedTransactions.isEmpty()) {
            // logger.info("No failed transactions to retry");
            return;
        }

        logger.info("Retrying failed transactions to KRA, count: " + failedTransactions.size());
        boolean reSend = false;

        for (EtimsSaveSaleTransaction transaction : failedTransactions) {
            try {
                // Mark as processing and update retry time
                etimsSaveSaleTransactionRepo.markAsProcessing(transaction.getId());
                etimsSaveSaleTransactionRepo.updateLastRetryTime(transaction.getId());

                logger.info("posting previously failed transaction to KRA, ID: " + transaction.getId());

                ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_TRNS_SALES_SAVE, "saveTrnsSalesVsdc",
                        TrnsSalesSaveWrReqBody.class);

                String tinBhfPath = transaction.getTin() + "_" + transaction.getBhfId();
                Long rcptTpNo = transaction.getRcptNo();
                Long rcptNo = transaction.getRcptNo();
                String invRcptKind = transaction.getInvRcptKind();

                Optional<EtimsSaveSaleReceipt> existingReceipt = etimsSaveSaleReceiptRepo
                        .findByEtimsSaveSaleTransactionID(transaction.getId());
                List<EtimsSaleItem> saleItems = etimsSaleItemRepository.findAllForSalesTransaction(transaction.getId());
                TrnsSalesSaveWrReq req = populateEtimsTrnsSalesObj(transaction, existingReceipt, saleItems);

                try {

                    // push data to KRA Server
                    String reSendId = transaction.getTin() + "_" + transaction.getBhfId() + "_"
                            + transaction.getInvcNo();
                    String resStr = this.apiClient.getClient(clientArg, req);
                    logger.info("apiClient resStr: " + resStr);

                    TrnsSalesSaveWrRes res = (TrnsSalesSaveWrRes) VsdcUtil.jsonToObject(resStr,
                            TrnsSalesSaveWrRes.class);
                    String resultCode = res.getResultCd();
                    logger.info("resultCode: " + resultCode);
                    logger.info("reSend: " + reSend);

                    if (resultCode.equals("910") || resultCode.equals("999") || resultCode.equals("901")) {

                        // 901 - Invalid Device
                        // 910 - Request parameter error
                        // 999 - Ask Administrator

                        // Failed due to itemList
                        transaction.setKraSendStatus("failed");
                        transaction.setKraResultCode(resultCode);

                        String errorMsg = res.getResultMsg();
                        String truncatedErrMsg = errorMsg.length() > 254 ? errorMsg.substring(0, 254) : errorMsg;
                        transaction.setKraResponseMessage(truncatedErrMsg);

                        etimsSaveSaleTransactionRepo.save(transaction);

                        ObjectMapper objectMapper = new ObjectMapper();
                        String jsonInputString = objectMapper.writeValueAsString(transaction);
                        logger.info("err json str=" + jsonInputString);

                        // ToDo: Send an email to admin with the invoice details
                        Thread.sleep(1000); // sleep for 1 second
                    }

                    if (!reSend)
                        if (resultCode.equals("896") || resultCode.equals("894")
                                || resultCode.equals("000")) {
                            SequenceManager.setRcptNo(rcptNo, tinBhfPath);
                            if ("serlInvNsr".equals(invRcptKind)) {
                                SequenceManager.setInvcNoNsr(rcptTpNo, tinBhfPath);
                            } else if ("serlInvTsr".equals(invRcptKind)) {
                                SequenceManager.setInvcNoTsr(rcptTpNo, tinBhfPath);
                            } else if ("serlInvPs".equals(invRcptKind)) {
                                SequenceManager.setInvcNoPs(rcptTpNo, tinBhfPath);
                            } else if ("serlInvCsr".equals(invRcptKind)) {
                                SequenceManager.setInvcNoCsr(rcptTpNo, tinBhfPath);
                            }
                        }

                    if (resultCode.equals("896") || resultCode.equals("894")
                            || resultCode.equals("000") || resultCode.equals("924")) {
                        logger.info("Data posted to KRA successfully.");

                        // After successful response from KRA, update the kra_send_status status to sent
                        // / success
                        transaction.setKraSendStatus("success");
                        transaction.setKraResultCode(resultCode);
                        transaction.setProcessingStatus(null);
                        transaction.setKraResponseMessage("Data posted to KRA successfully.");
                        etimsSaveSaleTransactionRepo.save(transaction);

                        // Add a ZReport entry
                        ZreportDailyReceipt receipt = new ZreportDailyReceipt();
                        receipt.setInvcNo(req.getInvcNo());
                        receipt.setRcptNo(transaction.getRcptNo());
                        receipt.setSalesTyCd(req.getSalesTyCd());
                        receipt.setRcptTyCd(req.getRcptTyCd());
                        receipt.setTotTaxblAmt(req.getTotTaxblAmt());
                        receipt.setTotTaxAmt(req.getTotTaxAmt());
                        DataZreportManager.saveZReportDailyFile(req.getTin(), req.getBhfId(),
                                res.getResultDt().substring(0, 8),
                                receipt, tinBhfPath);
                    } else {
                        logger.info("error posting data to KRA");
                        // Print out the structure of sent invoice for analysis
                        logger.info("err req: " + req);
                        logger.info("-------------------------------------------------------------------------");
                    }

                    if (!reSend)
                        if ("896".equals(res.getResultCd()) || "894".equals(res.getResultCd())) {
                            String reqJson = VsdcUtil.objectToJson(req);
                            this.resendMng.saveReSendFile("trnsSales", reSendId, reqJson, tinBhfPath);
                        }

                    // Send other pending data
                    // String filePathString = DataResendManager.getDataReSendPath(tinBhfPath,
                    // "trnsSales");
                    // Path resendPath = Paths.get(filePathString, new String[0]);
                    // if (!VsdcUtil.isEmpty(resendPath))
                    // if (this.checkConn.isConnected()) {
                    // this.resendMng.loadReSendFile("trnsSales", tinBhfPath);
                    // this.resendMng.loadReSendStockFile("stockMaster", tinBhfPath);
                    // this.resendMng.loadReSendStockIOFile("stockIO", tinBhfPath);
                    // this.resendMng.loadReSendItemFile("saveItem", tinBhfPath);
                    // this.resendMng.loadReSendImportsFile("trnsImports", tinBhfPath);
                    // this.resendMng.loadReSendPurchaseFile("trnsPurchase", tinBhfPath);
                    // }

                    Thread.sleep(1000); // sleep for 1 second

                } catch (Exception e) {
                    // e.printStackTrace();

                    logger.info("error on retry posting data to KRA, error=" + e.getMessage());
                    logger.info("-------------------------------------------------------------------------");
                }

            } catch (Exception e) {
                logger.info("error posting data to KRA, error=" + e.getMessage());
            } finally {
                // Clear processing status in case of success or failure
                etimsSaveSaleTransactionRepo.clearProcessingStatus(transaction.getId());
            }
        }
    }

    public TrnsSalesSaveWrReq populateEtimsTrnsSalesObj(EtimsSaveSaleTransaction transaction,
            Optional<EtimsSaveSaleReceipt> existingReceipt,
            List<EtimsSaleItem> saleItems) {
        TrnsSalesSaveWrReq req = new TrnsSalesSaveWrReq();

        Integer trxTotItemCnt = transaction.getTotItemCnt();
        Integer saleItemsSize = saleItems.size();

        logger.info("saleItemsSize: " + saleItemsSize + ", trxTotItemCnt: " + trxTotItemCnt);

        if (saleItemsSize != trxTotItemCnt) {
            logger.info("saleItemsSize != trxTotItemCnt");
            trxTotItemCnt = saleItemsSize;
        }

        req.setTin(transaction.getTin());
        req.setBhfId(transaction.getBhfId());
        req.setInvcNo(transaction.getInvcNo());
        req.setOrgInvcNo(transaction.getOrgInvcNo());
        req.setCustTin(transaction.getCustTin());
        req.setCustNm(transaction.getCustNm());
        req.setSalesTyCd(transaction.getSalesTyCd());
        req.setCfmDt(transaction.getCfmDt());
        req.setSalesDt(transaction.getSalesDt());
        req.setStockRlsDt(transaction.getStockRlsDt());
        req.setCnclReqDt(transaction.getCnclReqDt());
        req.setCnclDt(transaction.getCnclDt());
        req.setPmtTyCd(transaction.getPmtTyCd());
        req.setRcptTyCd(transaction.getRcptTyCd());
        req.setRegrNm(transaction.getRegrNm());
        req.setRegrId(transaction.getRegrId());
        req.setRfdDt(transaction.getRfdDt());
        req.setRfdRsnCd(transaction.getRfdRsnCd());
        req.setSalesSttsCd(transaction.getSalesSttsCd());
        req.setSalesTyCd(transaction.getSalesTyCd());
        req.setTotItemCnt(trxTotItemCnt);
        req.setTaxAmtA(transaction.getTaxAmtA());
        req.setTaxAmtB(transaction.getTaxAmtB());
        req.setTaxAmtC(transaction.getTaxAmtC());
        req.setTaxAmtD(transaction.getTaxAmtD());
        req.setTaxAmtE(transaction.getTaxAmtE());
        req.setTaxRtA(transaction.getTaxRtA());
        req.setTaxRtB(transaction.getTaxRtB());
        req.setTaxRtC(transaction.getTaxRtC());
        req.setTaxRtD(transaction.getTaxRtD());
        req.setTaxRtE(transaction.getTaxRtE());
        req.setTaxblAmtA(transaction.getTaxblAmtA());
        req.setTaxblAmtB(transaction.getTaxblAmtB());
        req.setTaxblAmtC(transaction.getTaxblAmtC());
        req.setTaxblAmtD(transaction.getTaxblAmtD());
        req.setTaxblAmtE(transaction.getTaxblAmtE());
        req.setTotTaxblAmt(transaction.getTotTaxblAmt());
        req.setTotTaxAmt(transaction.getTotTaxAmt());
        req.setTotAmt(transaction.getTotAmt());
        req.setPrchrAcptcYn(transaction.getPrchrAcptcYn());
        req.setRemark(transaction.getRemark());
        req.setRegrId(transaction.getRegrId());
        req.setModrId(transaction.getModrId());
        req.setModrNm(transaction.getModrNm());

        if (existingReceipt.isPresent()) {
            TrnsSalesSaveWrReceipt receipt = new TrnsSalesSaveWrReceipt();
            EtimsSaveSaleReceipt trxReceipt = existingReceipt.get();
            receipt.setAdrs(trxReceipt.getAdrs());
            receipt.setBtmMsg(trxReceipt.getBtmMsg());
            receipt.setCurRcptNo(trxReceipt.getCurRcptNo());
            receipt.setCustTin(trxReceipt.getCustTin());
            receipt.setCustMblNo(trxReceipt.getCustMblNo());
            receipt.setIntrlData(transaction.getIntrlData());
            receipt.setPrchrAcptcYn(trxReceipt.getPrchrAcptcYn());
            receipt.setRcptPbctDt(trxReceipt.getRcptPbctDt());
            receipt.setRcptSign(transaction.getRcptSign());
            receipt.setRptNo(trxReceipt.getRptNo());
            receipt.setTopMsg(trxReceipt.getTopMsg());
            receipt.setTotRcptNo(trxReceipt.getTotRcptNo());

            req.setReceipt(receipt);
        }

        List<TrnsSalesSaveWrItem> itemList = new ArrayList<>();
        for (EtimsSaleItem saleItem : saleItems) {
            TrnsSalesSaveWrItem item = new TrnsSalesSaveWrItem();
            item.setItemClsCd(saleItem.getItemClsCd());
            item.setItemSeq(saleItem.getItemSeq());
            item.setItemCd(saleItem.getItemCd());
            item.setItemNm(saleItem.getItemNm());
            item.setBcd(saleItem.getBcd());
            item.setPkgUnitCd(saleItem.getPkgUnitCd());
            item.setPkg(saleItem.getPkg());
            item.setQtyUnitCd(saleItem.getQtyUnitCd());
            item.setQty(saleItem.getQty());
            item.setPrc(saleItem.getPrc());
            item.setSplyAmt(saleItem.getSplyAmt());
            item.setDcRt(saleItem.getDcRt());
            item.setDcAmt(saleItem.getDcAmt());
            item.setIsrccCd(saleItem.getIsrccCd());
            item.setIsrccNm(saleItem.getIsrccNm());
            item.setIsrcRt(saleItem.getIsrcRt());
            item.setIsrcAmt(saleItem.getIsrcAmt());
            item.setTaxTyCd(saleItem.getTaxTyCd());
            item.setTaxblAmt(saleItem.getTaxblAmt());
            item.setTaxAmt(saleItem.getTaxAmt());
            item.setTotAmt(saleItem.getTotAmt());

            itemList.add(item);
        }

        req.setItemList(itemList);

        return req;
    }

}
