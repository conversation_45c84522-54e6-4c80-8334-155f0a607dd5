package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.manager.DeviceManager;
import com.struts.etims.config.manager.model.KeyInfo;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.InitInfoReq;
import com.struts.etims.execute.model.InitInfoReqBody;
import com.struts.etims.execute.model.InitInfoRes;
import com.struts.etims.manage.SequenceManager;

@RestController
@RequestMapping({ "/initializer" })
public class InitializeVsdcExcute {

    @Autowired
    ApiClient apiClient;

    @Autowired
    SequenceManager sequenceManager;

    @PostMapping({ "/selectInitInfo" })
    public InitInfoRes selectInitInfo(@RequestBody InitInfoReq req) {
        String resStr = "";
        InitInfoRes res = new InitInfoRes();
        // System.out
        // .println("req.getTin()=" + req.getTin() + ", req.getBhfId()=" +
        // req.getBhfId() + ", req.getDvcSrlNo()="
        // + req.getDvcSrlNo());
        String tinBhfPath = req.getTin() + "_" + req.getBhfId();
        // System.out.println("tinBhfPath=" + tinBhfPath);
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_INIT_INFO, "selectInitVsdcInfo",
                InitInfoReqBody.class);
        try {
            resStr = apiClient.getClient(clientArg, req, false);
            // System.out.println("resStr=" + resStr);
            res = (InitInfoRes) VsdcUtil.jsonToObject(resStr, InitInfoRes.class);
            // System.out.println(res.toString());
            if (res.getResultCd().equals("000")) {
                KeyInfo keyInfo = new KeyInfo();
                keyInfo.setCmcKey(res.getData().getInfo().getCmcKey());
                keyInfo.setIntrlKey(res.getData().getInfo().getIntrlKey());
                keyInfo.setSignKey(res.getData().getInfo().getSignKey());
                DeviceManager.setKey(keyInfo, tinBhfPath);
                res.getData().getInfo().setCmcKey(null);
                res.getData().getInfo().setIntrlKey(null);
                res.getData().getInfo().setSignKey(null);
                SequenceManager.setRcptNo(res.getData().getInfo().getLastSaleRcptNo(), tinBhfPath);
                res.getData().getInfo().setLastSaleRcptNo(null);
                DeviceManager.setMrcNo(res.getData().getInfo().getMrcNo(), tinBhfPath);
                res.getData().getInfo().setMrcNo(null);
                DeviceManager.setSdcID(res.getData().getInfo().getSdcId(), tinBhfPath);
                res.getData().getInfo().setSdcId(null);
                DeviceManager.setDevSerNo(req.getDvcSrlNo(), tinBhfPath);
                SequenceManager.setBhfOpenDt(res.getData().getInfo().getBhfOpenDt(), tinBhfPath);
                selectTotReceiptNo(req);
            }
        } catch (Exception e) {
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    public InitInfoRes selectTotReceiptNo(InitInfoReq req) {
        String resStr = "";
        InitInfoRes res = new InitInfoRes();
        String tinBhfPath = req.getTin() + "_" + req.getBhfId();
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_INIT_INFO, "selectInitInfoVsdcSeq",
                InitInfoReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req, false);
            res = (InitInfoRes) VsdcUtil.jsonToObject(resStr, InitInfoRes.class);
            if ("000".equals(res.getResultCd())) {
                SequenceManager.setRcptNo(res.getData().getInfo().getLastSaleRcptNo(), tinBhfPath);
                res.getData().getInfo().setLastSaleRcptNo(null);
                SequenceManager.setBhfOpenDt(res.getData().getInfo().getBhfOpenDt(), tinBhfPath);
                SequenceManager.setInvcNo(res.getData().getInfo().getLastInvcNo(), tinBhfPath);
                res.getData().getInfo().setLastInvcNo(null);
                SequenceManager.setInvcNoNsr(res.getData().getInfo().getLastSaleInvcNo(), tinBhfPath);
                res.getData().getInfo().setLastSaleInvcNo(null);
                SequenceManager.setInvcNoTsr(res.getData().getInfo().getLastTrainInvcNo(), tinBhfPath);
                res.getData().getInfo().setLastTrainInvcNo(null);
                SequenceManager.setInvcNoPs(res.getData().getInfo().getLastProfrmInvcNo(), tinBhfPath);
                res.getData().getInfo().setLastProfrmInvcNo(null);
                SequenceManager.setInvcNoCsr(res.getData().getInfo().getLastCopyInvcNo(), tinBhfPath);
                res.getData().getInfo().setLastCopyInvcNo(null);
            }
        } catch (Exception e) {
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
