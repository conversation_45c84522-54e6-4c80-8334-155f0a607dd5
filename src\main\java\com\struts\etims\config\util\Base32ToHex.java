package com.struts.etims.config.util;

public class Base32ToHex {
    private static final String base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";

    private static final int[] base32Lookup = new int[] {
            255, 255, 26, 27, 28, 29, 30, 31, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 0, 1, 2,
            3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
            13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
            23, 24, 25, 255, 255, 255, 255, 255, 255, 0,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
            11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
            21, 22, 23, 24, 25, 255, 255, 255, 255, 255 };

    public static String encode(byte[] bytes) {
        int i = 0, index = 0, digit = 0;
        StringBuffer base32 = new StringBuffer((bytes.length + 7) * 8 / 5);
        while (i < bytes.length) {
            int currByte = (bytes[i] >= 0) ? bytes[i] : (bytes[i] + 256);
            if (index > 3) {
                int nextByte;
                if (i + 1 < bytes.length) {
                    nextByte = (bytes[i + 1] >= 0) ? bytes[i + 1] : (bytes[i + 1] + 256);
                } else {
                    nextByte = 0;
                }
                digit = currByte & 255 >> index;
                index = (index + 5) % 8;
                digit <<= index;
                digit |= nextByte >> 8 - index;
                i++;
            } else {
                digit = currByte >> 8 - index + 5 & 0x1F;
                index = (index + 5) % 8;
                if (index == 0)
                    i++;
            }
            base32.append("ABCDEFGHIJKLMNOPQRSTUVWXYZ234567".charAt(digit));
        }
        return base32.toString();
    }

    public byte[] decode(String base32) {
        byte[] bytes = new byte[base32.length() * 5 / 8];
        for (int i = 0, index = 0, offset = 0; i < base32.length(); i++) {
            int lookup = base32.charAt(i) - 48;
            if (lookup >= 0 && lookup < base32Lookup.length) {
                int digit = base32Lookup[lookup];
                if (digit != 255)
                    if (index <= 3) {
                        index = (index + 5) % 8;
                        if (index == 0) {
                            bytes[offset] = (byte) (bytes[offset] | digit);
                            offset++;
                            if (offset >= bytes.length)
                                break;
                        } else {
                            bytes[offset] = (byte) (bytes[offset] | digit << 8 - index);
                        }
                    } else {
                        index = (index + 5) % 8;
                        bytes[offset] = (byte) (bytes[offset] | digit >>> index);
                        offset++;
                        if (offset >= bytes.length)
                            break;
                        bytes[offset] = (byte) (bytes[offset] | digit << 8 - index);
                    }
            }
        }
        return bytes;
    }

    public String convertToHex(byte[] data) {
        StringBuffer buf = new StringBuffer();
        for (int i = 0; i < data.length; i++) {
            int halfbyte = (data[i] >>> 4) & 0xF;
            int two_halfs = 0;
            do {
                if (0 <= halfbyte && halfbyte <= 9) {
                    buf.append((char) (48 + halfbyte)); // '0' + halfbyte
                } else {
                    buf.append((char) (97 + halfbyte - 10)); // 'a' + (halfbyte - 10)
                }
                halfbyte = data[i] & 0xF;
            } while (two_halfs++ < 1);
        }
        return buf.toString();
    }
}
