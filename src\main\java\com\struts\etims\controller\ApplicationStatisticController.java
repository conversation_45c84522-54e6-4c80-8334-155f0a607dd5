package com.struts.etims.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.forms.ApplicationStatisticForm;
import com.struts.etims.model.ApplicationStatisticList;
import com.struts.etims.model.ApplicationStatisticResponse;
import com.struts.etims.service.ApplicationStatisticService;

@RestController
@RequestMapping("/application-statistics")
public class ApplicationStatisticController {

    @Autowired
    ApplicationStatisticService applicationStatisticService;

    @PostMapping("")
    public ApplicationStatisticResponse createApplicationStatistic(@RequestBody ApplicationStatisticForm form) {
        return applicationStatisticService.save(form);
    }

    @GetMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public ApplicationStatisticList filterApplicationStatistics() {
        return applicationStatisticService.filterApplicationStatistics();
    }
}
