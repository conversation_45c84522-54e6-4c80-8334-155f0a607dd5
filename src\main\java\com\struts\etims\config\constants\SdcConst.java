package com.struts.etims.config.constants;

public class SdcConst {
    public static final String PROPERTIES_FILE_PATH = "application.properties";

    public static final String APPDATA_DATA_PATH = "AppData/EbmData";

    public static final String FILE_HISTORY_PATH = "Data/history";

    public static final String FILE_RESEND_PATH = "Data/resend";

    public static final String FILE_PROCESSED_PATH = "Data/processed";

    public static final String FILE_ZREPORT_PATH = "Data/zreport";

    public static final String RESEND_KIND_TRNS_SALES = "trnsSales";

    public static final String RESEND_KIND_TRNS_MASTER = "stockMaster";

    public static final String RESEND_KIND_TRNS_STOCK_IO = "stockIO";

    public static final String RESEND_KIND_TRNS_SAVE_ITEM = "saveItem";

    public static final String RESEND_KIND_TRNS_IMPORTS = "trnsImports";

    public static final String RESEND_KIND_TRNS_PURCHASE = "trnsPurchase";

    public static final String ZREPORT_KIND_REPORT = "report";

    public static final String ZREPORT_KIND_DALIY = "daily";

    public static final String CD_SALES_TY_C = "C";

    public static final String CD_SALES_TY_N = "N";

    public static final String CD_SALES_TY_P = "P";

    public static final String CD_SALES_TY_T = "T";

    public static final String CD_SALES_RCPT_TY_S = "S";

    public static final String CD_SALES_RCPT_TY_R = "R";

    public static final String SERIAL_KIND_RCPT_NUMBER = "rcptNo";

    public static final String SERIAL_KIND_BHF_OPEN_DT = "bhfOpenDt";

    public static final String SERIAL_KIND_INV_NUMBER_NO = "invcNo";

    public static final String SERIAL_KIND_INV_NUMBER_NSR = "serlInvNsr";

    public static final String SERIAL_KIND_INV_NUMBER_TSR = "serlInvTsr";

    public static final String SERIAL_KIND_INV_NUMBER_PS = "serlInvPs";

    public static final String SERIAL_KIND_INV_NUMBER_CSR = "serlInvCsr";
}
