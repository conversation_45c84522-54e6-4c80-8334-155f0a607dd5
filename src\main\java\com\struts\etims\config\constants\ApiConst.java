package com.struts.etims.config.constants;

public class ApiConst {
    public static final String RESULT_NAME_CD = "resultCd";

    public static final String RESULT_NAME_MSG = "resultMsg";

    public static final String RESULT_DEFAULT_SUCCESS = "000";

    public static final String API_RESULT_CLIENT_BUSINESS_RESEND_FILE_ERROR = "801";

    public static final String API_RESULT_CLIENT_BUSINESS_RESEND_FILE_ERROR_MSG = "There is no data to retransmit.";

    public static final String API_RESULT_CLIENT_BUSINESS_UNSENT_ERROR = "802";

    public static final String API_RESULT_CLIENT_BUSINESS_UNSENT_ERROR_MSG = "There is data that has not been transferred. After transfer is possible.";

    public static final String API_RESULT_CLIENT_BUSINESS_REPORT_SEND_ERROR = "803";

    public static final String API_RESULT_CLIENT_BUSINESS_REPORT_SEND_ERROR_MSG = "This is a report that transfer is complete.";

    public static final String API_RESULT_CLIENT_BUSINESS_REPORT_CREATE_ERROR = "804";

    public static final String API_RESULT_CLIENT_BUSINESS_REPORT_CREATE_ERROR_MSG = "There is no data to send for the report.";

    public static final String API_RESULT_CLIENT_BUSINESS_RESEND_DATA_ERROR = "805";

    public static final String API_RESULT_CLIENT_BUSINESS_RESEND_DATA_ERROR_MSG = "Corresponding retransmission data exists.";

    public static final String API_RESULT_CLIENT_REQUEST_URL_ERROR = "891";

    public static final String API_RESULT_CLIENT_REQUEST_URL_ERROR_MSG = "An error occurred while Request URL is created.";

    public static final String API_RESULT_CLIENT_REQUEST_HEADER_ERROR = "892";

    public static final String API_RESULT_CLIENT_REQUEST_HEADER_ERROR_MSG = "An error occurred while Request Header data is created.";

    public static final String API_RESULT_CLIENT_REQUEST_BODY_ERROR = "893";

    public static final String API_RESULT_CLIENT_REQUEST_BODY_ERROR_MSG = "An error occurred while Request Body data is created.";

    public static final String API_RESULT_CLIENT_REQUEST_COMMUNICATION_ERROR = "894";

    public static final String API_RESULT_CLIENT_REQUEST_COMMUNICATION_ERROR_MSG = "Connection to RRA API refused Check Connection.";

    public static final String API_RESULT_CLIENT_REQUEST_STATUS_ERROR = "896";

    public static final String API_RESULT_CLIENT_REQUEST_STATUS_ERROR_MSG = "An error regarding Request Status occurred. : ";

    public static final String API_RESULT_CLIENT_DEFAULT_ERROR = "899";

    public static final String API_RESULT_CLIENT_DEFAULT_ERROR_MSG = "An error regarding Client occurred.";

    public static final String SALES_RECEIPT_TYPES_MISMATCH_ERROR = "834";

    public static final String SALES_RECEIPT_TYPES_MISMATCH_MSG = "SalesType and ReceiptType must be NS-NR-TS-TR-CS-CR-PS check your inputs .";

    public static final String SEQUENCE_NUMBER_ALTERED_DELETED_ERROR = "836";

    public static final String SEQUENCE_NUMBER_ALTERED_DELETED_MSG = "Your Sequences have been altered, Connect to KRA API to get Sequences.";

    public static final String API_CONNECTION_DOWN_ERROR = "838";

    public static final String API_CONNECTION_DOWN_MSG = "Connection to API is not established: check connection";
}
