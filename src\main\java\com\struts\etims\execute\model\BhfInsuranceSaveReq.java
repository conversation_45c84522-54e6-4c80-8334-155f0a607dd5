package com.struts.etims.execute.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BhfInsuranceSaveReq {
    private String tin;

    private String bhfId;

    private String isrccCd;

    private String isrccNm;

    private BigDecimal isrcRt;

    private String useYn;

    private String regrId;

    private String regrNm;

    private String modrId;

    private String modrNm;
}
