package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.ImportItemReq;
import com.struts.etims.execute.model.ImportItemReqBody;
import com.struts.etims.execute.model.ImportItemRes;
import com.struts.etims.execute.model.ImportItemUpdateReq;
import com.struts.etims.execute.model.ImportItemUpdateReqBody;
import com.struts.etims.execute.model.ImportItemUpdateRes;

@RestController
@RequestMapping({ "/imports" })
public class ImportItemExcute {

    @Autowired
    ApiClient apiClient;

    @PostMapping({ "/selectImportItems" })
    public ImportItemRes selectImportItemList(@RequestBody ImportItemReq req) {
        String resStr = null;
        ImportItemRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_IMPORT_ITEM_SEARCH, "selectImportItemList",
                ImportItemReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (ImportItemRes) VsdcUtil.jsonToObject(resStr, ImportItemRes.class);
        } catch (Exception e) {
            res = new ImportItemRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/updateImportItems" })
    public ImportItemUpdateRes updateImportItem(@RequestBody ImportItemUpdateReq req) {
        String resStr = null;
        ImportItemUpdateRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_IMPORTITEM_UPDATE, "updateImportItem",
                ImportItemUpdateReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (ImportItemUpdateRes) VsdcUtil.jsonToObject(resStr, ImportItemUpdateRes.class);
        } catch (Exception e) {
            res = new ImportItemUpdateRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
