package com.struts.etims.manage;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.struts.etims.config.manager.DeviceManager;
import com.struts.etims.config.security.AesCoder;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.ReportZSaveReq;
import com.struts.etims.model.ZreportDaily;
import com.struts.etims.model.ZreportDailyReceipt;

@Component
public class DataZreportManager {
    private static AesCoder aesCoder;

    @Autowired
    public DataZreportManager(AesCoder aesCoder) {
        DataZreportManager.aesCoder = aesCoder;
    }

    private static String getDataZReportPath(String tinBhfPath, String[] folderPath) throws Exception {
        return VsdcUtil.createFilePath(
                "AppData/EbmData" + File.separator + tinBhfPath + File.separator + "Data/zreport", folderPath);
    }

    public static void saveZReportDailyFile(String tin, String bhfId, String rptDe,
            ZreportDailyReceipt receipt, String tinBhfPath) {
        try {
            if (!isSendZReport(rptDe, tinBhfPath)) {
                String dailyJson = null;
                ZreportDaily daily = null;
                String[] folderPaths = new String[3];
                folderPaths[0] = "daily";
                folderPaths[1] = rptDe.substring(0, 4);
                folderPaths[2] = rptDe.substring(4, 6);
                String dailyFilePath = getDataZReportPath(tinBhfPath, folderPaths) + File.separator + rptDe + ".json";
                File dailyFile = new File(dailyFilePath);
                if (!dailyFile.exists()) {
                    daily = new ZreportDaily();
                    daily.setTin(tin);
                    daily.setBhfId(bhfId);
                } else {
                    dailyJson = new String(Files.readAllBytes(Paths.get(dailyFilePath, new String[0])), "UTF-8");
                    daily = (ZreportDaily) VsdcUtil.jsonToObject(AesCoder.decrypt(dailyJson), ZreportDaily.class);
                }
                ZreportDailyReceipt zdr = null;
                List<ZreportDailyReceipt> receiptList = null;
                boolean dupRcpt = false;
                if (daily.getReceiptList() != null && daily.getReceiptList().size() > 0) {
                    receiptList = daily.getReceiptList();
                    for (int i = 0; i < receiptList.size(); i++) {
                        zdr = receiptList.get(i);
                        if (zdr.getInvcNo().equals(receipt.getInvcNo())) {
                            dupRcpt = true;
                            receiptList.set(i, receipt);
                        }
                    }
                    if (!dupRcpt)
                        receiptList.add(receipt);
                } else {
                    receiptList = new ArrayList<>();
                    receiptList.add(receipt);
                    daily.setReceiptList(receiptList);
                }
                daily.setReceiptList(receiptList);
                dailyJson = VsdcUtil.objectToJson(daily);
                Writer wr = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(dailyFilePath), "UTF-8"));
                wr.write(AesCoder.encrypt(dailyJson));
                wr.close();
            }
        } catch (Exception exception) {
        }
    }

    public static ReportZSaveReq createZReport(String rptDe, String tinBhfPath) throws Exception {
        ReportZSaveReq reportReq = null;
        ZreportDaily daily = null;
        String[] folderPaths = new String[3];
        folderPaths[0] = "daily";
        folderPaths[1] = rptDe.substring(0, 4);
        folderPaths[2] = rptDe.substring(4, 6);
        String dailyFilePath = getDataZReportPath(tinBhfPath, folderPaths) + File.separator + rptDe + ".json";
        File dailyFile = new File(dailyFilePath);
        if (!dailyFile.exists())
            return null;
        String dailyJson = new String(Files.readAllBytes(Paths.get(dailyFilePath, new String[0])), "UTF-8");
        daily = (ZreportDaily) VsdcUtil.jsonToObject(AesCoder.decrypt(dailyJson), ZreportDaily.class);
        if (daily.getReceiptList() == null || daily.getReceiptList().size() == 0)
            return null;
        double taxblAmt = 0.0D;
        double taxAmt = 0.0D;
        reportReq = new ReportZSaveReq();
        reportReq.setTin(daily.getTin());
        reportReq.setBhfId(daily.getBhfId());
        reportReq.setRptDe(rptDe);
        reportReq.setSdcId(DeviceManager.getSdcID(tinBhfPath));
        reportReq.setRptNo(SequenceManager.getRptNo(tinBhfPath));
        reportReq.setRegrId(DeviceManager.getSdcID(tinBhfPath));
        reportReq.setRegrNm(DeviceManager.getSdcID(tinBhfPath));
        for (ZreportDailyReceipt zdr : daily.getReceiptList()) {
            reportReq.setRcptPbctCnt(Integer.valueOf(reportReq.getRcptPbctCnt().intValue() + 1));
            if (reportReq.getRcptOpnNo().longValue() > 0L) {
                if (reportReq.getRcptOpnNo().longValue() > zdr.getRcptNo().longValue())
                    reportReq.setRcptOpnNo(zdr.getRcptNo());
            } else {
                reportReq.setRcptOpnNo(zdr.getRcptNo());
            }
            if (reportReq.getRcptClsNo().longValue() > 0L) {
                if (reportReq.getRcptClsNo().longValue() < zdr.getRcptNo().longValue())
                    reportReq.setRcptClsNo(zdr.getRcptNo());
            } else {
                reportReq.setRcptClsNo(zdr.getRcptNo());
            }
            if ("N".equals(zdr.getSalesTyCd())) {
                reportReq.setNrmRcptPbctCnt(Integer.valueOf(reportReq.getNrmRcptPbctCnt().intValue() + 1));
                if (reportReq.getNrmRcptOpnNo().longValue() > 0L) {
                    if (reportReq.getNrmRcptOpnNo().longValue() > zdr.getRcptNo().longValue())
                        reportReq.setNrmRcptOpnNo(zdr.getRcptNo());
                } else {
                    reportReq.setNrmRcptOpnNo(zdr.getRcptNo());
                }
                if (reportReq.getNrmRcptClsNo().longValue() > 0L) {
                    if (reportReq.getNrmRcptClsNo().longValue() < zdr.getRcptNo().longValue())
                        reportReq.setNrmRcptClsNo(zdr.getRcptNo());
                } else {
                    reportReq.setNrmRcptClsNo(zdr.getRcptNo());
                }
                if ("S".equals(zdr.getRcptTyCd())) {
                    taxblAmt = reportReq.getNrmSalesAmt().doubleValue() + zdr.getTotTaxblAmt().doubleValue();
                    taxAmt = reportReq.getNrmSalesTaxAmt().doubleValue() + zdr.getTotTaxAmt().doubleValue();
                    reportReq.setNrmSalesAmt(new BigDecimal(taxblAmt));
                    reportReq.setNrmSalesTaxAmt(new BigDecimal(taxAmt));
                    continue;
                }
                if ("R".equals(zdr.getRcptTyCd())) {
                    taxblAmt = reportReq.getNrmSalesAmt().doubleValue() + zdr.getTotTaxblAmt().doubleValue();
                    taxAmt = reportReq.getNrmSalesTaxAmt().doubleValue() + zdr.getTotTaxAmt().doubleValue();
                    reportReq.setNrmRfdAmt(new BigDecimal(taxblAmt));
                    reportReq.setNrmRfdTaxAmt(new BigDecimal(taxAmt));
                }
                continue;
            }
            if ("C".equals(zdr.getSalesTyCd())) {
                reportReq.setCpyRcptPbctCnt(Integer.valueOf(reportReq.getCpyRcptPbctCnt().intValue() + 1));
                if (reportReq.getCpyRcptOpnNo().longValue() > 0L) {
                    if (reportReq.getCpyRcptOpnNo().longValue() > zdr.getRcptNo().longValue())
                        reportReq.setCpyRcptOpnNo(zdr.getRcptNo());
                } else {
                    reportReq.setCpyRcptOpnNo(zdr.getRcptNo());
                }
                if (reportReq.getCpyRcptClsNo().longValue() > 0L) {
                    if (reportReq.getCpyRcptClsNo().longValue() < zdr.getRcptNo().longValue())
                        reportReq.setCpyRcptClsNo(zdr.getRcptNo());
                } else {
                    reportReq.setCpyRcptClsNo(zdr.getRcptNo());
                }
                if ("S".equals(zdr.getRcptTyCd())) {
                    taxblAmt = reportReq.getCpySalesAmt().doubleValue() + zdr.getTotTaxblAmt().doubleValue();
                    taxAmt = reportReq.getCpySalesTaxAmt().doubleValue() + zdr.getTotTaxAmt().doubleValue();
                    reportReq.setCpySalesAmt(new BigDecimal(taxblAmt));
                    reportReq.setCpySalesTaxAmt(new BigDecimal(taxAmt));
                    continue;
                }
                if ("R".equals(zdr.getRcptTyCd())) {
                    taxblAmt = reportReq.getCpySalesAmt().doubleValue() + zdr.getTotTaxblAmt().doubleValue();
                    taxAmt = reportReq.getCpySalesTaxAmt().doubleValue() + zdr.getTotTaxAmt().doubleValue();
                    reportReq.setCpyRfdAmt(new BigDecimal(taxblAmt));
                    reportReq.setCpyRfdTaxAmt(new BigDecimal(taxAmt));
                }
                continue;
            }
            if ("T".equals(zdr.getSalesTyCd())) {
                reportReq.setTrnRcptPbctCnt(Integer.valueOf(reportReq.getTrnRcptPbctCnt().intValue() + 1));
                if (reportReq.getTrnRcptOpnNo().longValue() > 0L) {
                    if (reportReq.getTrnRcptOpnNo().longValue() > zdr.getRcptNo().longValue())
                        reportReq.setTrnRcptOpnNo(zdr.getRcptNo());
                } else {
                    reportReq.setTrnRcptOpnNo(zdr.getRcptNo());
                }
                if (reportReq.getTrnRcptClsNo().longValue() > 0L) {
                    if (reportReq.getTrnRcptClsNo().longValue() < zdr.getRcptNo().longValue())
                        reportReq.setTrnRcptClsNo(zdr.getRcptNo());
                } else {
                    reportReq.setTrnRcptClsNo(zdr.getRcptNo());
                }
                if ("S".equals(zdr.getRcptTyCd())) {
                    taxblAmt = reportReq.getTrnSalesAmt().doubleValue() + zdr.getTotTaxblAmt().doubleValue();
                    taxAmt = reportReq.getTrnSalesTaxAmt().doubleValue() + zdr.getTotTaxAmt().doubleValue();
                    reportReq.setTrnSalesAmt(new BigDecimal(taxblAmt));
                    reportReq.setTrnSalesTaxAmt(new BigDecimal(taxAmt));
                    continue;
                }
                if ("R".equals(zdr.getRcptTyCd())) {
                    taxblAmt = reportReq.getTrnSalesAmt().doubleValue() + zdr.getTotTaxblAmt().doubleValue();
                    taxAmt = reportReq.getTrnSalesTaxAmt().doubleValue() + zdr.getTotTaxAmt().doubleValue();
                    reportReq.setTrnRfdAmt(new BigDecimal(taxblAmt));
                    reportReq.setTrnRfdTaxAmt(new BigDecimal(taxAmt));
                }
                continue;
            }
            if ("P".equals(zdr.getSalesTyCd())) {
                reportReq.setPfmRcptPbctCnt(Integer.valueOf(reportReq.getPfmRcptPbctCnt().intValue() + 1));
                if (reportReq.getPfmRcptOpnNo().longValue() > 0L) {
                    if (reportReq.getPfmRcptOpnNo().longValue() > zdr.getRcptNo().longValue())
                        reportReq.setPfmRcptOpnNo(zdr.getRcptNo());
                } else {
                    reportReq.setPfmRcptOpnNo(zdr.getRcptNo());
                }
                if (reportReq.getPfmRcptClsNo().longValue() > 0L) {
                    if (reportReq.getPfmRcptClsNo().longValue() < zdr.getRcptNo().longValue())
                        reportReq.setPfmRcptClsNo(zdr.getRcptNo());
                } else {
                    reportReq.setPfmRcptClsNo(zdr.getRcptNo());
                }
                if ("S".equals(zdr.getRcptTyCd())) {
                    taxblAmt = reportReq.getPfmSalesAmt().doubleValue() + zdr.getTotTaxblAmt().doubleValue();
                    taxAmt = reportReq.getPfmSalesTaxAmt().doubleValue() + zdr.getTotTaxAmt().doubleValue();
                    reportReq.setPfmSalesAmt(new BigDecimal(taxblAmt));
                    reportReq.setPfmSalesTaxAmt(new BigDecimal(taxAmt));
                    continue;
                }
                if ("R".equals(zdr.getRcptTyCd())) {
                    taxblAmt = reportReq.getPfmSalesAmt().doubleValue() + zdr.getTotTaxblAmt().doubleValue();
                    taxAmt = reportReq.getPfmSalesTaxAmt().doubleValue() + zdr.getTotTaxAmt().doubleValue();
                    reportReq.setPfmRfdAmt(new BigDecimal(taxblAmt));
                    reportReq.setPfmRfdTaxAmt(new BigDecimal(taxAmt));
                }
            }
        }
        String reportJson = VsdcUtil.objectToJson(reportReq);
        folderPaths[0] = "report";
        String reportFilePath = getDataZReportPath(tinBhfPath, folderPaths) + File.separator + rptDe + ".json";
        Writer wr = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(reportFilePath), "UTF-8"));
        wr.write(AesCoder.encrypt(reportJson));
        wr.close();
        return reportReq;
    }

    public static void updateZReport(String rptDe, String tinBhfPath) throws Exception {
        String[] folderPaths = new String[3];
        folderPaths[0] = "report";
        folderPaths[1] = rptDe.substring(0, 4);
        folderPaths[2] = rptDe.substring(4, 6);
        String reportFilePath = getDataZReportPath(tinBhfPath, folderPaths);
        File orgReportFile = new File(reportFilePath, rptDe + ".json");
        if (orgReportFile.exists() && orgReportFile.isFile())
            orgReportFile.renameTo(new File(reportFilePath, rptDe + "_OK.json"));
    }

    public static boolean isSendZReport(String rptDe, String tinBhfPath) throws Exception {
        String curDe = VsdcUtil.getDate("yyyyMMdd");
        if (Integer.valueOf(rptDe).intValue() >= Integer.valueOf(curDe).intValue())
            return false;
        String[] folderPaths = new String[3];
        folderPaths[0] = "report";
        folderPaths[1] = rptDe.substring(0, 4);
        folderPaths[2] = rptDe.substring(4, 6);
        String reportFilePath = getDataZReportPath(tinBhfPath, folderPaths) + File.separator + rptDe + "_OK.json";
        File reportFile = new File(reportFilePath);
        if (reportFile.exists())
            return true;
        return false;
    }
}
