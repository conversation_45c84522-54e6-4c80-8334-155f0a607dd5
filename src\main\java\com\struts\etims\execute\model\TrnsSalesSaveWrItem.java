package com.struts.etims.execute.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrnsSalesSaveWrItem {
    private Integer itemSeq;

    private String itemCd;

    private String itemClsCd;

    private String itemNm;

    private String bcd;

    private String pkgUnitCd;

    private BigDecimal pkg;

    private String qtyUnitCd;

    private BigDecimal qty;

    private BigDecimal prc;

    private BigDecimal splyAmt;

    private BigDecimal dcRt;

    private BigDecimal dcAmt;

    private String isrccCd;

    private String isrccNm;

    private BigDecimal isrcRt;

    private BigDecimal isrcAmt;

    private String taxTyCd;

    private BigDecimal taxblAmt;

    private BigDecimal taxAmt;

    private BigDecimal totAmt;
}
