package com.struts.etims.utils;

import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import com.struts.etims.entity.Email.EmailDetails;
import com.struts.etims.model.DashboardAnalytics;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class SendEmail {

    private static final Logger logger = LoggerFactory.getLogger(SendEmail.class);

    @Autowired
    private final JavaMailSender javaMailSender;

    @Autowired
    private final TemplateEngine templateEngine;

    @Value("${spring.mail.username}")
    private String sender;

    @Async
    public void sendEmail(String toEmail, String subject, String message) {
        try {
            SimpleMailMessage mailMessage = new SimpleMailMessage();
            mailMessage.setTo(toEmail);
            mailMessage.setSubject(subject);
            mailMessage.setText(message);
            mailMessage.setFrom(sender);
            javaMailSender.send(mailMessage);

            logger.info("Email sent successfully");

        } catch (Exception e) {
            logger.error("Error while Sending Mail {} ", e.getCause());
        }
    }

    @Async
    public void sendAnalyticsEmailWithTemplate(DashboardAnalytics dashboardAnalytics) {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage);

        Context context = new Context();
        context.setVariable("dashboardAnalytics", dashboardAnalytics);
        String process = templateEngine.process("emails/analytics", context);

        try {
            helper.setFrom(sender);
            helper.setSubject("Latest Report and Analytics");
            helper.setText(process, true);
            helper.setTo("<EMAIL>");
            javaMailSender.send(mimeMessage);

            logger.info("Email sent successfully");
        }

        // Catch block to handle MessagingException
        catch (MessagingException e) {
            // Display message when exception occurred
            logger.error("Error while Sending Mail {} ", e.getCause());
        }
    }

    @Async
    public void sendMailWithAttachment(EmailDetails details) {
        // Creating a mime message
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper;

        try {

            // Setting multipart as true for attachments to be sent
            mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
            mimeMessageHelper.setFrom(sender);
            mimeMessageHelper.setTo(details.getRecipient());
            mimeMessageHelper.setText(details.getMsgBody());
            mimeMessageHelper.setSubject(
                    details.getSubject());

            // Adding the attachment
            FileSystemResource file = new FileSystemResource(new File(details.getAttachment()));

            mimeMessageHelper.addAttachment(file.getFilename(), file);

            // Sending the mail
            javaMailSender.send(mimeMessage);

            logger.info("Email sent successfully");
        }

        // Catch block to handle MessagingException
        catch (MessagingException e) {
            // Display message when exception occurred
            logger.error("Error while Sending Mail {} ", e.getCause());
        }
    }

}
