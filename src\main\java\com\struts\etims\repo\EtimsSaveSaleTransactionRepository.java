package com.struts.etims.repo;

import com.struts.etims.entity.EtimsSaveSaleTransaction;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

public interface EtimsSaveSaleTransactionRepository extends JpaRepository<EtimsSaveSaleTransaction, Long> {

    Optional<EtimsSaveSaleTransaction> findByTinAndInvcNoAndRcptTyCd(String tin, Long invoiceNumber, String rcptTyCd);

    @Query("SELECT t FROM EtimsSaveSaleTransaction t WHERE t.kraSendStatus = 'pending' AND t.processingStatus IS NULL ORDER BY id DESC LIMIT 1")
    List<EtimsSaveSaleTransaction> findAllUnsentSalesTransactions();

    @Modifying
    @Transactional
    @Query("UPDATE EtimsSaveSaleTransaction t SET t.processingStatus = 'processing' WHERE t.id = :id")
    void markAsProcessing(Long id);

    @Modifying
    @Transactional
    @Query("UPDATE EtimsSaveSaleTransaction t SET t.processingStatus = NULL WHERE t.id = :id")
    void clearProcessingStatus(Long id);

    @Query("SELECT t FROM EtimsSaveSaleTransaction t WHERE t.kraSendStatus = 'failed' AND t.kraResultCode IN ('910', '999') "
            +
            "AND t.processingStatus IS NULL ORDER BY id DESC LIMIT 1")
    List<EtimsSaveSaleTransaction> findAllFailedSalesTransactions();

    @Modifying
    @Transactional
    @Query("UPDATE EtimsSaveSaleTransaction t SET t.lastRetryTime = CURRENT_TIMESTAMP WHERE t.id = :id")
    void updateLastRetryTime(Long id);
}
