package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.StockMasterSaveReq;
import com.struts.etims.execute.model.StockMasterSaveReqBody;
import com.struts.etims.execute.model.StockMasterSaveRes;
import com.struts.etims.manage.DataResendManager;

@RestController
@RequestMapping({ "/stockMaster" })
public class StockMasterExcute {

    @Autowired
    ApiClient apiClient;

    @Autowired
    DataResendManager resendMng;

    @PostMapping({ "/saveStockMaster" })
    public StockMasterSaveRes saveStockMaster(@RequestBody StockMasterSaveReq req) {
        String resStr = null;
        StockMasterSaveRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_STOCK_MASTER_SAVE, "saveStockMaster",
                StockMasterSaveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (StockMasterSaveRes) VsdcUtil.jsonToObject(resStr, StockMasterSaveRes.class);
            String reSendId = "stockMaster_" + VsdcUtil.getDate("yyyyMMddHHmmss");
            String tinBhfPath = req.getTin() + "_" + req.getBhfId();
            if ("896".equals(res.getResultCd()) || "894".equals(res.getResultCd())) {
                String reqJson = VsdcUtil.objectToJson(req);
                this.resendMng.saveReSendFile("stockMaster", reSendId, reqJson, tinBhfPath);
            }
        } catch (Exception e) {
            res = new StockMasterSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
