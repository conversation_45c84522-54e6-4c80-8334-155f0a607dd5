package com.struts.etims.worker;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.struts.etims.service.AnalyticsService;

@Component
public class LicenseWorker {

    private static final Logger logger = LoggerFactory.getLogger(LicenseWorker.class);

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");

    @Autowired
    private AnalyticsService analyticsService;

    // @Scheduled(cron = "0 0 */24 * * *") // runs every 24 hours
    public void CheckLicenses() {
        logger.info("Current time is :: {} ", dateFormat.format(new Date()));
        analyticsService.getDashboardAnalytics(false);
    }

    // @Scheduled(fixedRate = (60000 * 60 * 24)) // Run every day
    public void reportCurrentTime() {
        logger.info("The time is now {}", dateFormat.format(new Date()));

        // Can test functions here that I want to run at startup
        // analyticsService.getDashboardAnalytics(true);
    }
}
