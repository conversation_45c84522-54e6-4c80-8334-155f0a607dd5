package com.struts.etims.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.entity.License;
import com.struts.etims.model.LicenseList;
import com.struts.etims.service.LicenseService;

@RestController
@RequestMapping("/licenses")
public class LicenseController {

    @Autowired
    private LicenseService licenseService;

    @PostMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public License createLicense(@RequestBody License license) {
        return licenseService.save(license);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public License deleteLicenseByID(@PathVariable Long id) {
        return licenseService.deleteLicense(id);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public License findLicenseByID(@PathVariable int id) {
        return licenseService.getByID(id);
    }

    @GetMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public LicenseList filterLicenses() {
        return licenseService.filterLicenses();
    }

    @GetMapping("/about-to-expire")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public LicenseList filterLicensesAboutToExpire() {
        return licenseService.filterLicensesAboutToExpire();
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public License updateLicense(@RequestBody License license, @PathVariable int id) {
        return licenseService.updateLicense(license, id);
    }
}
