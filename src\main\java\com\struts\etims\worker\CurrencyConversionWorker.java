package com.struts.etims.worker;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.struts.etims.service.CurrencyConversionService;

@Component
public class CurrencyConversionWorker {

    private static final Logger logger = LoggerFactory.getLogger(CurrencyConversionWorker.class);

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");

    @Autowired
    private CurrencyConversionService currencyConversionService;

    // @Scheduled(cron = "0 0 */12 * * *") // runs every 12 hours
    public void CheckLicenses() {
        logger.info("{} fetching latest USD Exchange Rates", dateFormat.format(new Date()));
        currencyConversionService.fetchLatestExchangeRates();
    }

    // @Scheduled(cron = "0 0 */6 * * *") // runs every 6 hours
    public void fetchLatestBTCExchangeRates() {
        logger.info("{}, fetching latest BTC Exchange Rates", dateFormat.format(new Date()));
        currencyConversionService.fetchLatestBTCExchangeRates();
    }

}
