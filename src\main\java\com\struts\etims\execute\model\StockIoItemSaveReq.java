package com.struts.etims.execute.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockIoItemSaveReq {
    private Integer itemSeq;

    private String itemCd;

    private String itemClsCd;

    private String itemNm;

    private String bcd;

    private String pkgUnitCd;

    private BigDecimal pkg;

    private String qtyUnitCd;

    private BigDecimal qty;

    private String itemExprDt;

    private BigDecimal prc;

    private BigDecimal splyAmt;

    private BigDecimal totDcAmt;

    private BigDecimal taxblAmt;

    private String taxTyCd;

    private BigDecimal taxAmt;

    private BigDecimal totAmt;
}
