package com.struts.etims.execute;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.manager.DeviceManager;
import com.struts.etims.config.manager.InternalManager;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.entity.EtimsSaveSaleTransaction;
import com.struts.etims.execute.model.TrnsSalesSaveWrReq;
import com.struts.etims.execute.model.TrnsSalesSaveWrRes;
import com.struts.etims.execute.model.TrnsSalesSaveWrResData;
import com.struts.etims.execute.offline.CheckApiConnection;
import com.struts.etims.manage.DataResendManager;
import com.struts.etims.manage.DataZreportManager;
import com.struts.etims.manage.SequenceManager;
import com.struts.etims.service.ETIMSService;

@RestController
@RequestMapping({ "/trnsSales" })
public class TrnsSalesExcute {
    private static final Logger logger = LoggerFactory.getLogger(TrnsSalesExcute.class);

    // Constants for commonly used strings to improve performance and reduce string
    // literals
    private static final String SALES_TYPE_N = "N";
    private static final String SALES_TYPE_T = "T";
    private static final String SALES_TYPE_P = "P";
    private static final String SALES_TYPE_C = "C";
    private static final String RECEIPT_TYPE_S = "S";
    private static final String RECEIPT_TYPE_R = "R";
    private static final String SERIAL_INV_NSR = "serlInvNsr";
    private static final String SERIAL_INV_TSR = "serlInvTsr";
    private static final String SERIAL_INV_PS = "serlInvPs";
    private static final String SERIAL_INV_CSR = "serlInvCsr";
    private static final String RESULT_CODE_SUCCESS = "000";
    private static final String RESULT_CODE_TYPE_ERROR = "834";
    private static final String RESULT_CODE_SEQUENCE_ERROR = "836";
    private static final String RESULT_CODE_GENERAL_ERROR = "899";
    private static final String ERROR_TYPE_MSG = "SalesType and ReceiptType must be NS-NR-TS-TR-CS-CR-PS check your inputs .";
    private static final String ERROR_SEQUENCE_MSG = "Your Sequences have been altered, Connect to KRA API to get Sequences.";
    private static final String ERROR_DEVICE_DATA_MSG = "Failed to retrieve device data, please check configuration.";

    // Cache for device data to reduce repeated lookups
    private final Map<String, String> deviceCache = new ConcurrentHashMap<>();

    /**
     * Custom exception for device data retrieval issues
     */
    public static class DeviceDataException extends RuntimeException {
        private static final long serialVersionUID = 1L;

        public DeviceDataException(String message) {
            super(message);
        }

        public DeviceDataException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    @Autowired
    ApiClient apiClient;

    @Autowired
    DataResendManager resendMng;

    @Autowired
    CheckApiConnection checkConn;

    @Autowired
    DataZreportManager dataZrptMgr;

    @Autowired
    private ETIMSService etimsService;

    // Pre-initialized error responses for common error scenarios
    private final TrnsSalesSaveWrRes typeErrorResponse;
    private final TrnsSalesSaveWrRes sequenceErrorResponse;

    public TrnsSalesExcute() {
        // Initialize common error responses
        typeErrorResponse = new TrnsSalesSaveWrRes();
        typeErrorResponse.setResultCd(RESULT_CODE_TYPE_ERROR);
        typeErrorResponse.setResultMsg(ERROR_TYPE_MSG);

        sequenceErrorResponse = new TrnsSalesSaveWrRes();
        sequenceErrorResponse.setResultCd(RESULT_CODE_SEQUENCE_ERROR);
        sequenceErrorResponse.setResultMsg(ERROR_SEQUENCE_MSG);
    }

    @PostMapping({ "/saveSales" })
    public TrnsSalesSaveWrRes saveTrnsSales(@RequestBody TrnsSalesSaveWrReq req) {
        // Perform early validation to avoid unnecessary processing
        if (req == null || req.getTin() == null || req.getBhfId() == null) {
            TrnsSalesSaveWrRes res = new TrnsSalesSaveWrRes();
            res.setResultCd(RESULT_CODE_GENERAL_ERROR);
            res.setResultMsg("Missing required parameters");
            return res;
        }
        return saveTrnsSales(req, false);
    }

    /**
     * Safe method to compute cache values that handles exceptions
     * 
     * @param <K>             Key type
     * @param <V>             Value type
     * @param cache           The cache map
     * @param key             The key to look up
     * @param mappingFunction The function to compute the value
     * @param errorMsg        Error message if computation fails
     * @return The computed or cached value
     */
    private <K, V> V computeSafely(Map<K, V> cache, K key, Function<K, V> mappingFunction, String errorMsg) {
        try {
            // Check if key exists first to avoid potential exceptions in computeIfAbsent
            if (cache.containsKey(key)) {
                return cache.get(key);
            }

            // Compute value safely outside of computeIfAbsent
            V value = mappingFunction.apply(key);

            // Only cache non-null values
            if (value != null) {
                cache.put(key, value);
                return value;
            } else {
                throw new DeviceDataException(errorMsg + " - Null value returned");
            }
        } catch (Exception e) {
            // Log the exception and throw custom exception
            if (logger.isErrorEnabled()) {
                logger.error("Error retrieving device data: {} for key {}", e.getMessage(), key, e);
            }
            throw new DeviceDataException(errorMsg, e);
        }
    }

    /**
     * Get cached device serial number or retrieve it if not in cache
     * 
     * @param tinBhfPath The tin and bhf path
     * @return Device serial number
     * @throws DeviceDataException if unable to retrieve device serial number
     */
    private String getDeviceSerialNumber(String tinBhfPath) {
        String cacheKey = "dvcSrlNo_" + tinBhfPath;

        // Check cache and return device serial number
        if (deviceCache.containsKey(cacheKey)) {
            return deviceCache.get(cacheKey);
        }

        String deviceSerialNumber;

        try {
            deviceSerialNumber = DeviceManager.getDevSerNo(tinBhfPath);

            // Add to cache if not already present
            if (!deviceCache.containsKey(cacheKey)) {
                deviceCache.put(cacheKey, deviceSerialNumber);
            }
        } catch (Exception e) {
            throw new DeviceDataException("Failed to retrieve device serial number for " + tinBhfPath, e);
        }

        return deviceSerialNumber;
    }

    /**
     * Get cached SDC ID or retrieve it if not in cache
     * 
     * @param tinBhfPath The tin and bhf path
     * @return SDC ID
     * @throws DeviceDataException if unable to retrieve SDC ID
     */
    private String getSdcId(String tinBhfPath) {
        String cacheKey = "sdcId_" + tinBhfPath;

        // Check if existing in cache and return early
        if (deviceCache.containsKey(cacheKey)) {
            return deviceCache.get(cacheKey);
        }

        String sdcId;

        try {
            sdcId = DeviceManager.getSdcID(tinBhfPath);

            // Add to cache if not already present
            if (!deviceCache.containsKey(cacheKey)) {
                deviceCache.put(cacheKey, sdcId);
            }
        } catch (Exception e) {
            throw new DeviceDataException("Failed to retrieve SDC ID for " + tinBhfPath, e);
        }

        return sdcId;
    }

    /**
     * Get cached MRC number or retrieve it if not in cache
     * 
     * @param tinBhfPath The tin and bhf path
     * @return MRC number
     * @throws DeviceDataException if unable to retrieve MRC number
     */
    private String getMrcNumber(String tinBhfPath) {
        String cacheKey = "mrcNo_" + tinBhfPath;

        // Get from cache if present
        if (deviceCache.containsKey(cacheKey)) {
            return deviceCache.get(cacheKey);
        }

        String mrcNumber;

        try {
            mrcNumber = DeviceManager.getMrcNo(tinBhfPath);

            // Add to cache if not already present
            if (!deviceCache.containsKey(cacheKey)) {
                deviceCache.put(cacheKey, mrcNumber);
            }
        } catch (Exception e) {
            throw new DeviceDataException("Failed to retrieve MRC number for " + tinBhfPath, e);
        }

        return mrcNumber;
    }

    public TrnsSalesSaveWrRes saveTrnsSales(TrnsSalesSaveWrReq req, boolean reSend) {
        TrnsSalesSaveWrRes res = new TrnsSalesSaveWrRes();
        StringBuilder logBuilder = new StringBuilder(64);
        String tinBhfPath = req.getTin() + "_" + req.getBhfId();
        String dvcSrlNo = null;
        String invRcptKind = null;
        Long rcptNo = null;
        Long rcptTpNo = null;
        String vsdcRcptPbctDate = null;

        // Create the internal manager only when needed, not for every call
        InternalManager internalMngr = null;

        // Only log if debug is enabled to reduce overhead
        if (logger.isInfoEnabled()) {
            logBuilder.append("tinBhfPath=").append(tinBhfPath);
            logger.info(logBuilder.toString());
            logBuilder.setLength(0); // Reset the builder
        }

        // Optimize code for faster signing.

        try {
            if (!reSend) {
                try {
                    // Get device serial number from cache or retrieve it
                    dvcSrlNo = getDeviceSerialNumber(tinBhfPath);
                    System.out.println("dvcSrlNo: " + dvcSrlNo);
                } catch (DeviceDataException e) {
                    // Log the error and throw it to be caught by outer try-catch
                    if (logger.isErrorEnabled()) {
                        logBuilder.append("Failed to get device serial number: ").append(e.getMessage());
                        logger.error(logBuilder.toString(), e);
                        logBuilder.setLength(0);
                    }
                    throw e;
                }

                // Use constant strings instead of literals for better performance
                String salesType = req.getSalesTyCd();
                String receiptType = req.getRcptTyCd();

                // Optimized conditional checks using constants and more efficient structure
                if (SALES_TYPE_N.equals(salesType)) {
                    if (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType)) {
                        invRcptKind = SERIAL_INV_NSR;
                        rcptTpNo = SequenceManager.getRcptTypNo(req.getTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                                tinBhfPath);
                    } else {
                        return typeErrorResponse;
                    }
                } else if (SALES_TYPE_T.equals(salesType)) {
                    if (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType)) {
                        invRcptKind = SERIAL_INV_TSR;
                        rcptTpNo = SequenceManager.getRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                                tinBhfPath);
                    } else {
                        return typeErrorResponse;
                    }
                } else if (SALES_TYPE_P.equals(salesType)) {
                    if (RECEIPT_TYPE_S.equals(receiptType)) {
                        invRcptKind = SERIAL_INV_PS;
                        rcptTpNo = SequenceManager.getRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                                tinBhfPath);
                    } else {
                        return typeErrorResponse;
                    }
                } else if (SALES_TYPE_C.equals(salesType)) {
                    if (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType)) {
                        invRcptKind = SERIAL_INV_CSR;
                        rcptTpNo = SequenceManager.getRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                                tinBhfPath);
                    } else {
                        return typeErrorResponse;
                    }
                } else {
                    return typeErrorResponse;
                }
                // Check receipt type number validity
                if (rcptTpNo != null && rcptTpNo.longValue() > 0) {
                    req.getReceipt().setCurRcptNo(rcptTpNo);
                } else {
                    return sequenceErrorResponse;
                }

                // Get receipt number once and validate it
                rcptNo = SequenceManager.getRcptNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, tinBhfPath);
                if (rcptNo != null && rcptNo.longValue() > 0) {
                    req.getReceipt().setTotRcptNo(rcptNo);
                } else {
                    return sequenceErrorResponse;
                }

                // Format date efficiently
                vsdcRcptPbctDate = VsdcUtil.DateStringFormater(System.currentTimeMillis());
                req.getReceipt().setRcptPbctDt(vsdcRcptPbctDate);
                req.getReceipt().setRptNo(SequenceManager.getRptNo(tinBhfPath));

                // Skip signature generation for certain sales types
                if (SALES_TYPE_P.equals(req.getSalesTyCd()) || SALES_TYPE_T.equals(req.getSalesTyCd())) {
                    req.getReceipt().setIntrlData("");
                    req.getReceipt().setRcptSign("");
                } else {
                    // Only log if debug logging is enabled
                    if (logger.isInfoEnabled()) {
                        logBuilder.append("tinBhfPath: ").append(tinBhfPath);
                        logger.info(logBuilder.toString());
                        logBuilder.setLength(0);
                    }

                    // Initialize InternalManager only when needed
                    if (internalMngr == null) {
                        internalMngr = new InternalManager();
                    }

                    // Calculate the values once to avoid multiple conversions
                    double taxAmtB = req.getTaxAmtB().doubleValue();
                    long rptNo = req.getReceipt().getRptNo().longValue();
                    long invcNo = req.getInvcNo().longValue();
                    double taxblAmtB = req.getTaxblAmtB().doubleValue();
                    double taxblAmtA = req.getTaxblAmtA().doubleValue();
                    double taxAmtA = req.getTaxAmtA().doubleValue();

                    // Get internal data
                    String intrlData = internalMngr.getInternalData(
                            salesType, receiptType, taxAmtB, rptNo, invcNo, tinBhfPath);
                    req.getReceipt().setIntrlData(intrlData);

                    // Get signature data
                    String signData = internalMngr.getSignature(
                            vsdcRcptPbctDate, req.getTin(), req.getCustTin(),
                            invcNo, taxblAmtB, taxAmtB, taxblAmtA, taxAmtA,
                            salesType, receiptType, invcNo, invcNo, tinBhfPath);
                    req.getReceipt().setRcptSign(signData);
                }
            }

            // Create response data object once and populate efficiently
            TrnsSalesSaveWrResData resData = new TrnsSalesSaveWrResData();

            // Use cached values for better performance with proper error handling
            try {
                resData.setSdcId(getSdcId(tinBhfPath));
                resData.setMrcNo(getMrcNumber(tinBhfPath));
            } catch (DeviceDataException e) {
                // Log the error but continue with empty values rather than failing completely
                if (logger.isWarnEnabled()) {
                    logBuilder.append("Using empty device data due to error: ").append(e.getMessage());
                    logger.warn(logBuilder.toString(), e);
                    logBuilder.setLength(0);
                }
                // Set empty values as fallback
                resData.setSdcId("");
                resData.setMrcNo("");
            }

            // Set receipt data from request
            resData.setRcptNo(req.getReceipt().getCurRcptNo());
            resData.setTotRcptNo(req.getReceipt().getTotRcptNo());
            resData.setVsdcRcptPbctDate(req.getReceipt().getRcptPbctDt());
            resData.setIntrlData(req.getReceipt().getIntrlData());
            resData.setRcptSign(req.getReceipt().getRcptSign());

            // Set sales status and type
            req.setSalesSttsCd("02");
            req.setSalesTyCd(SALES_TYPE_N);

            // Save data to database
            EtimsSaveSaleTransaction savedTransaction = etimsService.saveReceiptDetailsToDB(req, resData, invRcptKind);

            // Log results conditionally to improve performance
            if (logger.isInfoEnabled()) {
                if (savedTransaction.getId() == 0) {
                    logger.info("failed to save ETIMS transaction to DB");
                } else {
                    logBuilder.append("saved ETIMS transaction to DB, id: ").append(savedTransaction.getId());
                    logger.info(logBuilder.toString());
                    logBuilder.setLength(0);
                }
            }

            // Set success response
            res.setResultCd(RESULT_CODE_SUCCESS);
            res.setResultMsg("Successful");
            res.setData(resData);

        } catch (DeviceDataException e) {
            // Specific handling for device data errors
            if (logger.isErrorEnabled()) {
                logBuilder.append("Device data error: ").append(e.getMessage());
                logger.error(logBuilder.toString(), e);
                logBuilder.setLength(0);
            }

            // Create specific response for device data error
            res = new TrnsSalesSaveWrRes();
            res.setResultCd(RESULT_CODE_GENERAL_ERROR);
            res.setResultMsg(ERROR_DEVICE_DATA_MSG + ": " + e.getMessage());
        } catch (Exception e) {
            // Better exception handling with specific logging
            if (logger.isErrorEnabled()) {
                logBuilder.append("Error processing sales transaction: ").append(e.getMessage());
                logger.error(logBuilder.toString(), e);
                logBuilder.setLength(0);
            }

            // Create new response for error case
            res = new TrnsSalesSaveWrRes();
            res.setResultCd(RESULT_CODE_GENERAL_ERROR);
            res.setResultMsg("An error regarding Client occurred: " + e.getMessage());
        }
        return res;
    }
}
