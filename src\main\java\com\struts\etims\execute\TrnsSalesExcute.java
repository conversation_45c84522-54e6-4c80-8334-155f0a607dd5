package com.struts.etims.execute;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.manager.DeviceManager;
import com.struts.etims.config.util.Base32;
import com.struts.etims.config.util.Base32ToHex;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.entity.EtimsSaveSaleTransaction;
import com.struts.etims.execute.model.TrnsSalesSaveWrReq;
import com.struts.etims.execute.model.TrnsSalesSaveWrRes;
import com.struts.etims.execute.model.TrnsSalesSaveWrResData;
import com.struts.etims.execute.offline.CheckApiConnection;
import com.struts.etims.manage.DataResendManager;
import com.struts.etims.manage.DataZreportManager;
import com.struts.etims.manage.SequenceManager;
import com.struts.etims.service.ETIMSService;

@RestController
@RequestMapping({ "/trnsSales" })
public class TrnsSalesExcute {
    private static final Logger logger = LoggerFactory.getLogger(TrnsSalesExcute.class);

    // Constants for commonly used strings to improve performance and reduce string
    // literals
    private static final String SALES_TYPE_N = "N";
    private static final String SALES_TYPE_T = "T";
    private static final String SALES_TYPE_P = "P";
    private static final String SALES_TYPE_C = "C";
    private static final String RECEIPT_TYPE_S = "S";
    private static final String RECEIPT_TYPE_R = "R";
    private static final String SERIAL_INV_NSR = "serlInvNsr";
    private static final String SERIAL_INV_TSR = "serlInvTsr";
    private static final String SERIAL_INV_PS = "serlInvPs";
    private static final String SERIAL_INV_CSR = "serlInvCsr";
    private static final String RESULT_CODE_SUCCESS = "000";
    private static final String RESULT_CODE_TYPE_ERROR = "834";
    private static final String RESULT_CODE_SEQUENCE_ERROR = "836";
    private static final String RESULT_CODE_GENERAL_ERROR = "899";
    private static final String ERROR_TYPE_MSG = "SalesType and ReceiptType must be NS-NR-TS-TR-CS-CR-PS check your inputs .";
    private static final String ERROR_SEQUENCE_MSG = "Your Sequences have been altered, Connect to KRA API to get Sequences.";
    private static final String ERROR_DEVICE_DATA_MSG = "Failed to retrieve device data, please check configuration.";

    // Enhanced caching for better performance - AGGRESSIVE CACHING FOR 5ms TARGET
    private final Map<String, String> deviceCache = new ConcurrentHashMap<>();
    private final Map<String, String> keyCache = new ConcurrentHashMap<>();
    private final Map<String, String> hexKeyCache = new ConcurrentHashMap<>();
    private final Map<String, CachedSequence> sequenceCache = new ConcurrentHashMap<>();

    // Pre-loaded data to eliminate ALL file I/O during request processing
    private final Map<String, DeviceData> preloadedDeviceData = new ConcurrentHashMap<>();
    private final Map<String, CryptoKeys> preloadedCryptoKeys = new ConcurrentHashMap<>();

    // Pre-initialized Base32 converters for better performance
    private static final Base32ToHex BASE32_CONVERTER = new Base32ToHex();
    private static final Base32 BASE32_HMAC = new Base32();

    // Cache TTL settings (in milliseconds)
    private static final long SEQUENCE_CACHE_TTL = 300000; // 5 minutes
    private static final long KEY_CACHE_TTL = 3600000; // 1 hour

    /**
     * Cached sequence number with timestamp for TTL management
     */
    private static class CachedSequence {
        final Long value;
        final long timestamp;

        CachedSequence(Long value) {
            this.value = value;
            this.timestamp = System.currentTimeMillis();
        }

        boolean isExpired(long ttlMs) {
            return System.currentTimeMillis() - timestamp > ttlMs;
        }
    }

    /**
     * Pre-loaded device data to eliminate file I/O
     */
    private static class DeviceData {
        final String dvcSrlNo;
        final String sdcId;
        final String mrcNo;
        final long loadTime;

        DeviceData(String dvcSrlNo, String sdcId, String mrcNo) {
            this.dvcSrlNo = dvcSrlNo;
            this.sdcId = sdcId;
            this.mrcNo = mrcNo;
            this.loadTime = System.currentTimeMillis();
        }
    }

    /**
     * Pre-loaded crypto keys to eliminate file I/O and decryption
     */
    private static class CryptoKeys {
        final String intrlKey;
        final String signKey;
        final String cmcKey;
        final String hexKey; // Pre-computed hex key
        final long loadTime;

        CryptoKeys(String intrlKey, String signKey, String cmcKey, String hexKey) {
            this.intrlKey = intrlKey;
            this.signKey = signKey;
            this.cmcKey = cmcKey;
            this.hexKey = hexKey;
            this.loadTime = System.currentTimeMillis();
        }
    }

    /**
     * Custom exception for device data retrieval issues
     */
    public static class DeviceDataException extends RuntimeException {
        private static final long serialVersionUID = 1L;

        public DeviceDataException(String message) {
            super(message);
        }

        public DeviceDataException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    @Autowired
    ApiClient apiClient;

    @Autowired
    DataResendManager resendMng;

    @Autowired
    CheckApiConnection checkConn;

    @Autowired
    DataZreportManager dataZrptMgr;

    @Autowired
    private ETIMSService etimsService;

    // @Autowired(required = false)
    // private com.struts.etims.service.PerformanceMonitoringService
    // performanceMonitoringService;

    // Pre-initialized error responses for common error scenarios
    private final TrnsSalesSaveWrRes typeErrorResponse;
    private final TrnsSalesSaveWrRes sequenceErrorResponse;

    public TrnsSalesExcute() {
        // Initialize common error responses
        typeErrorResponse = new TrnsSalesSaveWrRes();
        typeErrorResponse.setResultCd(RESULT_CODE_TYPE_ERROR);
        typeErrorResponse.setResultMsg(ERROR_TYPE_MSG);

        sequenceErrorResponse = new TrnsSalesSaveWrRes();
        sequenceErrorResponse.setResultCd(RESULT_CODE_SEQUENCE_ERROR);
        sequenceErrorResponse.setResultMsg(ERROR_SEQUENCE_MSG);

        // Pre-load all device data and crypto keys on startup to eliminate file I/O
        preloadAllData();
    }

    /**
     * CRITICAL: Pre-load ALL data to eliminate file I/O during request processing
     * This is essential for achieving 5ms response time
     */
    private void preloadAllData() {
        // This should be called on application startup
        // Pre-load common device paths and their data
        try {
            // Common device paths - add more as needed
            String[] commonPaths = {
                    "P051922564N_00", "P051922564N_01",
                    "P051468536N_00", "P051468536N_01" // Added the missing path
            };

            for (String tinBhfPath : commonPaths) {
                try {
                    preloadDeviceData(tinBhfPath);
                    preloadCryptoKeys(tinBhfPath);
                    logger.info("Successfully pre-loaded data for: {}", tinBhfPath);
                } catch (Exception e) {
                    logger.warn("Failed to preload data for {}: {}", tinBhfPath, e.getMessage());
                }
            }

            logger.info("Data pre-loading completed. Loaded {} device configurations.", commonPaths.length);
        } catch (Exception e) {
            logger.error("Error during data preloading: {}", e.getMessage());
        }
    }

    /**
     * Pre-load device data for ultra-fast access
     */
    private void preloadDeviceData(String tinBhfPath) throws Exception {
        String dvcSrlNo = DeviceManager.getDevSerNo(tinBhfPath);
        String sdcId = DeviceManager.getSdcID(tinBhfPath);
        String mrcNo = DeviceManager.getMrcNo(tinBhfPath);

        DeviceData deviceData = new DeviceData(dvcSrlNo, sdcId, mrcNo);
        preloadedDeviceData.put(tinBhfPath, deviceData);

        // Also populate the regular cache
        deviceCache.put("dvcSrlNo_" + tinBhfPath, dvcSrlNo);
        deviceCache.put("sdcId_" + tinBhfPath, sdcId);
        deviceCache.put("mrcNo_" + tinBhfPath, mrcNo);
    }

    /**
     * Pre-load crypto keys for ultra-fast access
     */
    private void preloadCryptoKeys(String tinBhfPath) throws Exception {
        String intrlKey = DeviceManager.getKey("intrlKey", tinBhfPath);
        String signKey = DeviceManager.getKey("signKey", tinBhfPath);
        String cmcKey = DeviceManager.getKey("cmcKey", tinBhfPath);

        // Pre-compute hex key to avoid conversion during request
        byte[] base32DecodedData = BASE32_CONVERTER.decode(intrlKey);
        String hexKey = BASE32_CONVERTER.convertToHex(base32DecodedData).toUpperCase();

        CryptoKeys cryptoKeys = new CryptoKeys(intrlKey, signKey, cmcKey, hexKey);
        preloadedCryptoKeys.put(tinBhfPath, cryptoKeys);

        // Also populate the regular caches
        keyCache.put("intrlKey_" + tinBhfPath, intrlKey);
        keyCache.put("signKey_" + tinBhfPath, signKey);
        keyCache.put("cmcKey_" + tinBhfPath, cmcKey);
        hexKeyCache.put(intrlKey, hexKey);
    }

    /**
     * Add a new device path for pre-loading (can be called at runtime)
     */
    public void addDevicePathForPreloading(String tinBhfPath) {
        try {
            if (!preloadedDeviceData.containsKey(tinBhfPath)) {
                preloadDeviceData(tinBhfPath);
                logger.info("Successfully added device data for: {}", tinBhfPath);
            }

            if (!preloadedCryptoKeys.containsKey(tinBhfPath)) {
                preloadCryptoKeys(tinBhfPath);
                logger.info("Successfully added crypto keys for: {}", tinBhfPath);
            }
        } catch (Exception e) {
            logger.error("Failed to add device path {}: {}", tinBhfPath, e.getMessage());
        }
    }

    /**
     * Get current pre-loaded device paths
     */
    public String getPreloadedDevicePaths() {
        return "Device Data: " + preloadedDeviceData.keySet() +
                ", Crypto Keys: " + preloadedCryptoKeys.keySet();
    }

    @PostMapping({ "/saveSales" })
    public TrnsSalesSaveWrRes saveTrnsSales(@RequestBody TrnsSalesSaveWrReq req) {
        // Perform early validation to avoid unnecessary processing
        if (req == null || req.getTin() == null || req.getBhfId() == null) {
            TrnsSalesSaveWrRes res = new TrnsSalesSaveWrRes();
            res.setResultCd(RESULT_CODE_GENERAL_ERROR);
            res.setResultMsg("Missing required parameters");
            return res;
        }
        return saveTrnsSalesUltraFast(req, false);
    }

    /**
     * Debug endpoint to check pre-loaded device paths
     */
    @org.springframework.web.bind.annotation.GetMapping({ "/preloadedPaths" })
    public String getPreloadedPaths() {
        return getPreloadedDevicePaths();
    }

    /**
     * Endpoint to manually add a device path for pre-loading
     */
    @PostMapping({ "/addDevicePath" })
    public String addDevicePath(@org.springframework.web.bind.annotation.RequestParam String tinBhfPath) {
        addDevicePathForPreloading(tinBhfPath);
        return "Added device path: " + tinBhfPath + ". Current paths: " + getPreloadedDevicePaths();
    }

    /**
     * ULTRA-FAST VERSION: Target 5ms response time
     * Eliminates all file I/O, minimizes object creation, uses pre-loaded data
     */
    public TrnsSalesSaveWrRes saveTrnsSalesUltraFast(TrnsSalesSaveWrReq req, boolean reSend) {
        // Minimal object creation - reuse where possible
        TrnsSalesSaveWrRes res = new TrnsSalesSaveWrRes();
        String tinBhfPath = req.getTin() + "_" + req.getBhfId();

        try {
            if (!reSend) {
                // ULTRA-FAST: Get all data from pre-loaded cache (NO FILE I/O)
                String dvcSrlNo = getDeviceSerialNumberUltraFast(tinBhfPath);
                CryptoKeys cryptoKeys = getCryptoKeysUltraFast(tinBhfPath);

                // Fast sales type validation and sequence retrieval
                String salesType = req.getSalesTyCd();
                String receiptType = req.getRcptTyCd();
                String invRcptKind;
                Long rcptTpNo;

                // Optimized conditional logic
                if (SALES_TYPE_N.equals(salesType)
                        && (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType))) {
                    invRcptKind = SERIAL_INV_NSR;
                    rcptTpNo = getOptimizedRcptTypNo(req.getTin(), req.getBhfId(), dvcSrlNo, invRcptKind, tinBhfPath);
                } else if (SALES_TYPE_T.equals(salesType)
                        && (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType))) {
                    invRcptKind = SERIAL_INV_TSR;
                    rcptTpNo = getOptimizedRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                            tinBhfPath);
                } else if (SALES_TYPE_P.equals(salesType) && RECEIPT_TYPE_S.equals(receiptType)) {
                    invRcptKind = SERIAL_INV_PS;
                    rcptTpNo = getOptimizedRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                            tinBhfPath);
                } else if (SALES_TYPE_C.equals(salesType)
                        && (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType))) {
                    invRcptKind = SERIAL_INV_CSR;
                    rcptTpNo = getOptimizedRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                            tinBhfPath);
                } else {
                    return typeErrorResponse;
                }

                if (rcptTpNo == null || rcptTpNo.longValue() <= 0) {
                    return sequenceErrorResponse;
                }
                req.getReceipt().setCurRcptNo(rcptTpNo);

                // Get receipt number
                Long rcptNo = getOptimizedRcptNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, tinBhfPath);
                if (rcptNo == null || rcptNo.longValue() <= 0) {
                    return sequenceErrorResponse;
                }
                req.getReceipt().setTotRcptNo(rcptNo);

                // Fast date formatting
                String vsdcRcptPbctDate = VsdcUtil.DateStringFormater(System.currentTimeMillis());
                req.getReceipt().setRcptPbctDt(vsdcRcptPbctDate);
                req.getReceipt().setRptNo(SequenceManager.getRptNo(tinBhfPath));

                // Ultra-fast signature generation for non-P/T types
                if (!SALES_TYPE_P.equals(salesType) && !SALES_TYPE_T.equals(salesType)) {
                    // Pre-calculate values once
                    double taxAmtB = req.getTaxAmtB().doubleValue();
                    long rptNo = req.getReceipt().getRptNo().longValue();
                    long invcNo = req.getInvcNo().longValue();
                    double taxblAmtB = req.getTaxblAmtB().doubleValue();
                    double taxblAmtA = req.getTaxblAmtA().doubleValue();
                    double taxAmtA = req.getTaxAmtA().doubleValue();

                    // Get device data ultra-fast
                    String mrcNo = getMrcNumberUltraFast(tinBhfPath);
                    String sdcId = getSdcIdUltraFast(tinBhfPath);

                    // Ultra-fast internal data and signature generation
                    String intrlData = getOptimizedInternalData(salesType, receiptType, taxAmtB, rptNo, invcNo,
                            cryptoKeys.intrlKey);
                    req.getReceipt().setIntrlData(intrlData);

                    String signData = getOptimizedSignature(vsdcRcptPbctDate, req.getTin(), req.getCustTin(),
                            invcNo, taxblAmtB, taxAmtB, taxblAmtA, taxAmtA,
                            salesType, receiptType, invcNo, invcNo, mrcNo, sdcId, cryptoKeys.signKey);
                    req.getReceipt().setRcptSign(signData);
                } else {
                    req.getReceipt().setIntrlData("");
                    req.getReceipt().setRcptSign("");
                }

                // Set sales status
                req.setSalesSttsCd("02");
                req.setSalesTyCd(SALES_TYPE_N);
            }

            // Create response data efficiently
            TrnsSalesSaveWrResData resData = new TrnsSalesSaveWrResData();
            resData.setSdcId(getSdcIdUltraFast(tinBhfPath));
            resData.setMrcNo(getMrcNumberUltraFast(tinBhfPath));
            resData.setRcptNo(req.getReceipt().getCurRcptNo());
            resData.setTotRcptNo(req.getReceipt().getTotRcptNo());
            resData.setVsdcRcptPbctDate(req.getReceipt().getRcptPbctDt());
            resData.setIntrlData(req.getReceipt().getIntrlData());
            resData.setRcptSign(req.getReceipt().getRcptSign());

            // ASYNC: Save to database (don't wait for completion)
            CompletableFuture.runAsync(() -> {
                try {
                    etimsService.saveReceiptDetailsToDB(req, resData, "");
                } catch (Exception e) {
                    logger.error("Async database save failed: {}", e.getMessage());
                }
            });

            // Set success response
            res.setResultCd(RESULT_CODE_SUCCESS);
            res.setResultMsg("Successful");
            res.setData(resData);

        } catch (Exception e) {
            res.setResultCd(RESULT_CODE_GENERAL_ERROR);
            res.setResultMsg("Error: " + e.getMessage());
        }

        return res;
    }

    /**
     * Safe method to compute cache values that handles exceptions
     * 
     * @param <K>             Key type
     * @param <V>             Value type
     * @param cache           The cache map
     * @param key             The key to look up
     * @param mappingFunction The function to compute the value
     * @param errorMsg        Error message if computation fails
     * @return The computed or cached value
     */
    private <K, V> V computeSafely(Map<K, V> cache, K key, Function<K, V> mappingFunction, String errorMsg) {
        try {
            // Check if key exists first to avoid potential exceptions in computeIfAbsent
            if (cache.containsKey(key)) {
                return cache.get(key);
            }

            // Compute value safely outside of computeIfAbsent
            V value = mappingFunction.apply(key);

            // Only cache non-null values
            if (value != null) {
                cache.put(key, value);
                return value;
            } else {
                throw new DeviceDataException(errorMsg + " - Null value returned");
            }
        } catch (Exception e) {
            // Log the exception and throw custom exception
            if (logger.isErrorEnabled()) {
                logger.error("Error retrieving device data: {} for key {}", e.getMessage(), key, e);
            }
            throw new DeviceDataException(errorMsg, e);
        }
    }

    /**
     * Get cached cryptographic key or retrieve it if not in cache
     *
     * @param keyType    The type of key (intrlKey, signKey, cmcKey)
     * @param tinBhfPath The tin and bhf path
     * @return Cached or retrieved key
     * @throws DeviceDataException if unable to retrieve key
     */
    private String getCachedKey(String keyType, String tinBhfPath) {
        String cacheKey = keyType + "_" + tinBhfPath;

        return keyCache.computeIfAbsent(cacheKey, k -> {
            try {
                return DeviceManager.getKey(keyType, tinBhfPath);
            } catch (Exception e) {
                throw new DeviceDataException("Failed to retrieve " + keyType + " for " + tinBhfPath, e);
            }
        });
    }

    /**
     * Get cached hex key or compute it if not in cache
     *
     * @param intrlKey The internal key to convert
     * @return Hex representation of the key
     */
    private String getCachedHexKey(String intrlKey) {
        return hexKeyCache.computeIfAbsent(intrlKey, key -> {
            byte[] base32DecodedData = BASE32_CONVERTER.decode(key);
            return BASE32_CONVERTER.convertToHex(base32DecodedData).toUpperCase();
        });
    }

    /**
     * Get cached sequence number with TTL support
     *
     * @param cacheKey The cache key for the sequence
     * @param supplier Function to retrieve the sequence if not cached or expired
     * @return Cached or retrieved sequence number
     */
    private Long getCachedSequence(String cacheKey, Function<String, Long> supplier) {
        CachedSequence cached = sequenceCache.get(cacheKey);

        if (cached != null && !cached.isExpired(SEQUENCE_CACHE_TTL)) {
            return cached.value;
        }

        // Remove expired entry and compute new value
        sequenceCache.remove(cacheKey);
        Long newValue = supplier.apply(cacheKey);

        if (newValue != null) {
            sequenceCache.put(cacheKey, new CachedSequence(newValue));
        }

        return newValue;
    }

    /**
     * ULTRA-FAST: Get device serial number from pre-loaded data (NO FILE I/O)
     * Target: <0.1ms with fallback for dynamic loading
     */
    private String getDeviceSerialNumberUltraFast(String tinBhfPath) {
        DeviceData deviceData = preloadedDeviceData.get(tinBhfPath);
        if (deviceData != null) {
            return deviceData.dvcSrlNo;
        }

        // FALLBACK: If not pre-loaded, try to load dynamically
        logger.warn("Device data not pre-loaded for {}. Loading dynamically (performance impact expected).",
                tinBhfPath);
        try {
            preloadDeviceData(tinBhfPath);
            deviceData = preloadedDeviceData.get(tinBhfPath);
            if (deviceData != null) {
                return deviceData.dvcSrlNo;
            }
        } catch (Exception e) {
            logger.error("Failed to dynamically load device data for {}: {}", tinBhfPath, e.getMessage());
        }

        // Fallback to cache if not pre-loaded
        String cacheKey = "dvcSrlNo_" + tinBhfPath;
        String cached = deviceCache.get(cacheKey);
        if (cached != null) {
            return cached;
        }

        // Last resort - file I/O (should be avoided for 5ms target)
        try {
            String deviceSerialNumber = DeviceManager.getDevSerNo(tinBhfPath);
            deviceCache.put(cacheKey, deviceSerialNumber);
            return deviceSerialNumber;
        } catch (Exception e) {
            throw new DeviceDataException("Failed to retrieve device serial number for " + tinBhfPath, e);
        }
    }

    /**
     * ULTRA-FAST: Get SDC ID from pre-loaded data (NO FILE I/O)
     * Target: <0.1ms
     */
    private String getSdcIdUltraFast(String tinBhfPath) {
        DeviceData deviceData = preloadedDeviceData.get(tinBhfPath);
        if (deviceData != null) {
            return deviceData.sdcId;
        }

        // Fallback to existing method
        return getSdcId(tinBhfPath);
    }

    /**
     * ULTRA-FAST: Get MRC number from pre-loaded data (NO FILE I/O)
     * Target: <0.1ms
     */
    private String getMrcNumberUltraFast(String tinBhfPath) {
        DeviceData deviceData = preloadedDeviceData.get(tinBhfPath);
        if (deviceData != null) {
            return deviceData.mrcNo;
        }

        // Fallback to existing method
        return getMrcNumber(tinBhfPath);
    }

    /**
     * ULTRA-FAST: Get crypto keys from pre-loaded data (NO FILE I/O, NO DECRYPTION)
     * Target: <0.1ms with fallback for dynamic loading
     */
    private CryptoKeys getCryptoKeysUltraFast(String tinBhfPath) {
        CryptoKeys cryptoKeys = preloadedCryptoKeys.get(tinBhfPath);
        if (cryptoKeys != null) {
            return cryptoKeys;
        }

        // FALLBACK: If not pre-loaded, load dynamically (will be slower but functional)
        logger.warn("Crypto keys not pre-loaded for {}. Loading dynamically (performance impact expected).",
                tinBhfPath);
        try {
            preloadCryptoKeys(tinBhfPath);
            cryptoKeys = preloadedCryptoKeys.get(tinBhfPath);
            if (cryptoKeys != null) {
                return cryptoKeys;
            }
        } catch (Exception e) {
            logger.error("Failed to dynamically load crypto keys for {}: {}", tinBhfPath, e.getMessage());
        }

        throw new DeviceDataException("Failed to load crypto keys for " + tinBhfPath +
                ". Check device configuration and file permissions.");
    }

    /**
     * Get cached device serial number or retrieve it if not in cache
     *
     * @param tinBhfPath The tin and bhf path
     * @return Device serial number
     * @throws DeviceDataException if unable to retrieve device serial number
     */
    private String getDeviceSerialNumber(String tinBhfPath) {
        String cacheKey = "dvcSrlNo_" + tinBhfPath;

        // Check cache and return device serial number
        if (deviceCache.containsKey(cacheKey)) {
            return deviceCache.get(cacheKey);
        }

        String deviceSerialNumber;

        try {
            deviceSerialNumber = DeviceManager.getDevSerNo(tinBhfPath);

            // Add to cache if not already present
            if (!deviceCache.containsKey(cacheKey)) {
                deviceCache.put(cacheKey, deviceSerialNumber);
            }
        } catch (Exception e) {
            throw new DeviceDataException("Failed to retrieve device serial number for " + tinBhfPath, e);
        }

        return deviceSerialNumber;
    }

    /**
     * Get cached SDC ID or retrieve it if not in cache
     * 
     * @param tinBhfPath The tin and bhf path
     * @return SDC ID
     * @throws DeviceDataException if unable to retrieve SDC ID
     */
    private String getSdcId(String tinBhfPath) {
        String cacheKey = "sdcId_" + tinBhfPath;

        // Check if existing in cache and return early
        if (deviceCache.containsKey(cacheKey)) {
            return deviceCache.get(cacheKey);
        }

        String sdcId;

        try {
            sdcId = DeviceManager.getSdcID(tinBhfPath);

            // Add to cache if not already present
            if (!deviceCache.containsKey(cacheKey)) {
                deviceCache.put(cacheKey, sdcId);
            }
        } catch (Exception e) {
            throw new DeviceDataException("Failed to retrieve SDC ID for " + tinBhfPath, e);
        }

        return sdcId;
    }

    /**
     * Get cached MRC number or retrieve it if not in cache
     * 
     * @param tinBhfPath The tin and bhf path
     * @return MRC number
     * @throws DeviceDataException if unable to retrieve MRC number
     */
    private String getMrcNumber(String tinBhfPath) {
        String cacheKey = "mrcNo_" + tinBhfPath;

        // Get from cache if present
        if (deviceCache.containsKey(cacheKey)) {
            return deviceCache.get(cacheKey);
        }

        String mrcNumber;

        try {
            mrcNumber = DeviceManager.getMrcNo(tinBhfPath);

            // Add to cache if not already present
            if (!deviceCache.containsKey(cacheKey)) {
                deviceCache.put(cacheKey, mrcNumber);
            }
        } catch (Exception e) {
            throw new DeviceDataException("Failed to retrieve MRC number for " + tinBhfPath, e);
        }

        return mrcNumber;
    }

    /**
     * Async processing for non-critical operations to improve response time
     */
    @Async
    public CompletableFuture<Void> processNonCriticalOperations(TrnsSalesSaveWrReq req, String tinBhfPath) {
        try {
            // Log transaction details asynchronously
            if (logger.isInfoEnabled()) {
                logger.info("Async processing for transaction: TIN={}, InvcNo={}, Amount={}",
                        req.getTin(), req.getInvcNo(), req.getTotAmt());
            }

            // Additional audit logging or analytics can be added here
            // without affecting the main transaction response time

        } catch (Exception e) {
            logger.warn("Error in async processing for transaction {}: {}", req.getInvcNo(), e.getMessage());
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * Batch processing for multiple transactions
     */
    @Transactional
    public List<TrnsSalesSaveWrRes> saveBatchTrnsSales(List<TrnsSalesSaveWrReq> requests) {
        return requests.stream()
                .map(req -> saveTrnsSales(req, false))
                .collect(Collectors.toList());
    }

    /**
     * Optimized sequence number retrieval with caching
     */
    private Long getOptimizedRcptNo(String tin, String bhf, String dvcSrlNo, String tinBhfPath) {
        String cacheKey = "rcptNo_" + tinBhfPath;
        return getCachedSequence(cacheKey, k -> {
            try {
                return SequenceManager.getRcptNo(tin, bhf, dvcSrlNo, tinBhfPath);
            } catch (Exception e) {
                logger.error("Error retrieving receipt number: {}", e.getMessage());
                return null;
            }
        });
    }

    /**
     * Optimized receipt type number retrieval with caching
     */
    private Long getOptimizedRcptTypNo(String tin, String bhf, String dvcSrlNo, String invRcptKind, String tinBhfPath) {
        String cacheKey = "rcptTypNo_" + invRcptKind + "_" + tinBhfPath;
        return getCachedSequence(cacheKey, k -> {
            try {
                return SequenceManager.getRcptTypNo(tin, bhf, dvcSrlNo, invRcptKind, tinBhfPath);
            } catch (Exception e) {
                logger.error("Error retrieving receipt type number: {}", e.getMessage());
                return null;
            }
        });
    }

    /**
     * Optimized internal data generation using cached keys and converters
     */
    private String getOptimizedInternalData(String salesTyCd, String rcptTyCd, double taxTyBTaxAmt,
            long rptNo, long totInvcNoCnt, String intrlKey) throws Exception {

        // Pre-calculate values to avoid multiple conversions
        int intNSTaxAmtB = 0;
        int intNRTaxAmtB = 0;

        if ("N".equals(salesTyCd)) {
            if ("S".equals(rcptTyCd)) {
                intNSTaxAmtB = (int) taxTyBTaxAmt;
            } else {
                intNRTaxAmtB = (int) taxTyBTaxAmt;
            }
        }

        int intTaxTyBTaxAmt = (int) Math.floor(taxTyBTaxAmt);
        intNRTaxAmtB += intTaxTyBTaxAmt;

        // Use StringBuilder with initial capacity for better performance
        StringBuilder sb = new StringBuilder(32);
        sb.append(com.struts.etims.config.util.AesEnc.longToHex(intNSTaxAmtB, 5));
        sb.append(com.struts.etims.config.util.AesEnc.longToHex(intNRTaxAmtB, 5));
        sb.append(com.struts.etims.config.util.AesEnc.longToHex(rptNo, 2));
        sb.append(com.struts.etims.config.util.AesEnc.longToHex(totInvcNoCnt, 4));

        // Get cached hex key
        String hexKey = getCachedHexKey(intrlKey);

        // Use AesEnc for encryption
        com.struts.etims.config.util.AesEnc internal = new com.struts.etims.config.util.AesEnc();
        return internal.encrypt(sb.toString(), hexKey);
    }

    /**
     * Optimized signature generation using cached keys and pre-initialized
     * converters
     */
    private String getOptimizedSignature(String rcptDt, String tin, String custTin, long invcNo,
            double taxTyBTaxblAmt, double taxTyBTaxAmt, double taxTyATaxblAmt,
            double taxTyATaxAmt, String salesTyCd, String rcptTyCd,
            long totInvcNoCnt, long salesTyTotInvcNoCnt, String mrcNo,
            String sdcId, String signKey) throws Exception {

        // Use StringBuilder with estimated capacity for better performance
        StringBuilder sb = new StringBuilder(256);

        // Pre-format amounts to avoid repeated string operations
        String formattedTaxTyBTaxblAmt = com.struts.etims.config.util.SkmmUtil
                .lpadAmount(String.format("%.2f", taxTyBTaxblAmt), 15);
        String formattedTaxTyBTaxAmt = com.struts.etims.config.util.SkmmUtil
                .lpadAmount(String.format("%.2f", taxTyBTaxAmt), 15);
        String formattedTaxTyATaxblAmt = com.struts.etims.config.util.SkmmUtil
                .lpadAmount(String.format("%.2f", taxTyATaxblAmt), 15);
        String formattedTaxTyATaxAmt = com.struts.etims.config.util.SkmmUtil
                .lpadAmount(String.format("%.2f", taxTyATaxAmt), 15);

        // Build signature string efficiently
        sb.append(rcptDt)
                .append(tin)
                .append(custTin != null ? custTin : "")
                .append(com.struts.etims.config.util.SkmmUtil.lpad(String.valueOf(invcNo), 10))
                .append(mrcNo)
                .append(formattedTaxTyBTaxblAmt)
                .append(formattedTaxTyBTaxAmt)
                .append(formattedTaxTyATaxblAmt)
                .append(formattedTaxTyATaxAmt)
                .append(com.struts.etims.config.util.SkmmUtil.lpadAmount("0,00", 15))
                .append(com.struts.etims.config.util.SkmmUtil.lpadAmount("0,00", 15))
                .append(com.struts.etims.config.util.SkmmUtil.lpadAmount("0,00", 15))
                .append(com.struts.etims.config.util.SkmmUtil.lpadAmount("0,00", 15))
                .append(com.struts.etims.config.util.SkmmUtil.lpadAmount("0,00", 15))
                .append(com.struts.etims.config.util.SkmmUtil.lpadAmount("0,00", 15))
                .append(salesTyCd)
                .append(rcptTyCd)
                .append(sdcId)
                .append(rcptDt)
                .append(com.struts.etims.config.util.SkmmUtil.lpad(String.valueOf(totInvcNoCnt), 10))
                .append(com.struts.etims.config.util.SkmmUtil.lpad(String.valueOf(salesTyTotInvcNoCnt), 10));

        // Use pre-initialized Base32 instance for HMAC
        return BASE32_HMAC.hmacSha1(sb.toString(), signKey);
    }

    public TrnsSalesSaveWrRes saveTrnsSales(TrnsSalesSaveWrReq req, boolean reSend) {
        long startTime = System.currentTimeMillis();
        TrnsSalesSaveWrRes res = new TrnsSalesSaveWrRes();
        StringBuilder logBuilder = new StringBuilder(64);
        String tinBhfPath = req.getTin() + "_" + req.getBhfId();
        String dvcSrlNo = null;
        String invRcptKind = null;
        Long rcptNo = null;
        Long rcptTpNo = null;
        String vsdcRcptPbctDate = null;

        // Only log if debug is enabled to reduce overhead
        if (logger.isInfoEnabled()) {
            logBuilder.append("tinBhfPath=").append(tinBhfPath);
            logger.info(logBuilder.toString());
            logBuilder.setLength(0); // Reset the builder
        }

        // Optimize code for faster signing.

        try {
            if (!reSend) {
                try {
                    // Get device serial number from cache or retrieve it
                    dvcSrlNo = getDeviceSerialNumber(tinBhfPath);
                    System.out.println("dvcSrlNo: " + dvcSrlNo);
                } catch (DeviceDataException e) {
                    // Log the error and throw it to be caught by outer try-catch
                    if (logger.isErrorEnabled()) {
                        logBuilder.append("Failed to get device serial number: ").append(e.getMessage());
                        logger.error(logBuilder.toString(), e);
                        logBuilder.setLength(0);
                    }
                    throw e;
                }

                // Use constant strings instead of literals for better performance
                String salesType = req.getSalesTyCd();
                String receiptType = req.getRcptTyCd();

                // Optimized conditional checks using constants and more efficient structure
                if (SALES_TYPE_N.equals(salesType)) {
                    if (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType)) {
                        invRcptKind = SERIAL_INV_NSR;
                        rcptTpNo = getOptimizedRcptTypNo(req.getTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                                tinBhfPath);
                    } else {
                        return typeErrorResponse;
                    }
                } else if (SALES_TYPE_T.equals(salesType)) {
                    if (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType)) {
                        invRcptKind = SERIAL_INV_TSR;
                        rcptTpNo = getOptimizedRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                                tinBhfPath);
                    } else {
                        return typeErrorResponse;
                    }
                } else if (SALES_TYPE_P.equals(salesType)) {
                    if (RECEIPT_TYPE_S.equals(receiptType)) {
                        invRcptKind = SERIAL_INV_PS;
                        rcptTpNo = getOptimizedRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                                tinBhfPath);
                    } else {
                        return typeErrorResponse;
                    }
                } else if (SALES_TYPE_C.equals(salesType)) {
                    if (RECEIPT_TYPE_S.equals(receiptType) || RECEIPT_TYPE_R.equals(receiptType)) {
                        invRcptKind = SERIAL_INV_CSR;
                        rcptTpNo = getOptimizedRcptTypNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, invRcptKind,
                                tinBhfPath);
                    } else {
                        return typeErrorResponse;
                    }
                } else {
                    return typeErrorResponse;
                }
                // Check receipt type number validity
                if (rcptTpNo != null && rcptTpNo.longValue() > 0) {
                    req.getReceipt().setCurRcptNo(rcptTpNo);
                } else {
                    return sequenceErrorResponse;
                }

                // Get receipt number using optimized caching
                rcptNo = getOptimizedRcptNo(req.getCustTin(), req.getBhfId(), dvcSrlNo, tinBhfPath);
                if (rcptNo != null && rcptNo.longValue() > 0) {
                    req.getReceipt().setTotRcptNo(rcptNo);
                } else {
                    return sequenceErrorResponse;
                }

                // Format date efficiently
                vsdcRcptPbctDate = VsdcUtil.DateStringFormater(System.currentTimeMillis());
                req.getReceipt().setRcptPbctDt(vsdcRcptPbctDate);
                req.getReceipt().setRptNo(SequenceManager.getRptNo(tinBhfPath));

                // Skip signature generation for certain sales types
                if (SALES_TYPE_P.equals(req.getSalesTyCd()) || SALES_TYPE_T.equals(req.getSalesTyCd())) {
                    req.getReceipt().setIntrlData("");
                    req.getReceipt().setRcptSign("");
                } else {
                    // Only log if debug logging is enabled
                    if (logger.isInfoEnabled()) {
                        logBuilder.append("tinBhfPath: ").append(tinBhfPath);
                        logger.info(logBuilder.toString());
                        logBuilder.setLength(0);
                    }

                    // Calculate the values once to avoid multiple conversions
                    double taxAmtB = req.getTaxAmtB().doubleValue();
                    long rptNo = req.getReceipt().getRptNo().longValue();
                    long invcNo = req.getInvcNo().longValue();
                    double taxblAmtB = req.getTaxblAmtB().doubleValue();
                    double taxblAmtA = req.getTaxblAmtA().doubleValue();
                    double taxAmtA = req.getTaxAmtA().doubleValue();

                    // Get cached keys for better performance
                    String intrlKey = getCachedKey("intrlKey", tinBhfPath);
                    String signKey = getCachedKey("signKey", tinBhfPath);
                    String mrcNo = getMrcNumber(tinBhfPath);
                    String sdcId = getSdcId(tinBhfPath);

                    // Get internal data using optimized method
                    String intrlData = getOptimizedInternalData(salesType, receiptType, taxAmtB, rptNo, invcNo,
                            intrlKey);
                    req.getReceipt().setIntrlData(intrlData);

                    // Get signature data using optimized method
                    String signData = getOptimizedSignature(vsdcRcptPbctDate, req.getTin(), req.getCustTin(),
                            invcNo, taxblAmtB, taxAmtB, taxblAmtA, taxAmtA,
                            salesType, receiptType, invcNo, invcNo, mrcNo, sdcId, signKey);
                    req.getReceipt().setRcptSign(signData);
                }
            }

            // Create response data object once and populate efficiently
            TrnsSalesSaveWrResData resData = new TrnsSalesSaveWrResData();

            // Use cached values for better performance with proper error handling
            try {
                resData.setSdcId(getSdcId(tinBhfPath));
                resData.setMrcNo(getMrcNumber(tinBhfPath));
            } catch (DeviceDataException e) {
                // Log the error but continue with empty values rather than failing completely
                if (logger.isWarnEnabled()) {
                    logBuilder.append("Using empty device data due to error: ").append(e.getMessage());
                    logger.warn(logBuilder.toString(), e);
                    logBuilder.setLength(0);
                }
                // Set empty values as fallback
                resData.setSdcId("");
                resData.setMrcNo("");
            }

            // Set receipt data from request
            resData.setRcptNo(req.getReceipt().getCurRcptNo());
            resData.setTotRcptNo(req.getReceipt().getTotRcptNo());
            resData.setVsdcRcptPbctDate(req.getReceipt().getRcptPbctDt());
            resData.setIntrlData(req.getReceipt().getIntrlData());
            resData.setRcptSign(req.getReceipt().getRcptSign());

            // Set sales status and type
            req.setSalesSttsCd("02");
            req.setSalesTyCd(SALES_TYPE_N);

            // Save data to database
            EtimsSaveSaleTransaction savedTransaction = etimsService.saveReceiptDetailsToDB(req, resData, invRcptKind);

            // Log results conditionally to improve performance
            if (logger.isInfoEnabled()) {
                if (savedTransaction.getId() == 0) {
                    logger.info("failed to save ETIMS transaction to DB");
                } else {
                    logBuilder.append("saved ETIMS transaction to DB, id: ").append(savedTransaction.getId());
                    logger.info(logBuilder.toString());
                    logBuilder.setLength(0);
                }
            }

            // Process non-critical operations asynchronously to improve response time
            if (!reSend) {
                processNonCriticalOperations(req, tinBhfPath);
            }

            // Set success response
            res.setResultCd(RESULT_CODE_SUCCESS);
            res.setResultMsg("Successful");
            res.setData(resData);

        } catch (DeviceDataException e) {
            // Specific handling for device data errors
            if (logger.isErrorEnabled()) {
                logBuilder.append("Device data error: ").append(e.getMessage());
                logger.error(logBuilder.toString(), e);
                logBuilder.setLength(0);
            }

            // Create specific response for device data error
            res = new TrnsSalesSaveWrRes();
            res.setResultCd(RESULT_CODE_GENERAL_ERROR);
            res.setResultMsg(ERROR_DEVICE_DATA_MSG + ": " + e.getMessage());
        } catch (Exception e) {
            // Better exception handling with specific logging
            if (logger.isErrorEnabled()) {
                logBuilder.append("Error processing sales transaction: ").append(e.getMessage());
                logger.error(logBuilder.toString(), e);
                logBuilder.setLength(0);
            }

            // Create new response for error case
            res = new TrnsSalesSaveWrRes();
            res.setResultCd(RESULT_CODE_GENERAL_ERROR);
            res.setResultMsg("An error regarding Client occurred: " + e.getMessage());
        }
        return res;
    }

    /**
     * Clear all caches to prevent memory leaks in long-running applications
     * This method should be called periodically or when memory pressure is detected
     */
    public void clearAllCaches() {
        deviceCache.clear();
        keyCache.clear();
        hexKeyCache.clear();
        sequenceCache.clear();

        if (logger.isInfoEnabled()) {
            logger.info("All caches cleared successfully");
        }
    }

    /**
     * Get cache statistics for monitoring and debugging
     */
    public String getCacheStatistics() {
        return String.format(
                "Cache Statistics - Device: %d, Keys: %d, HexKeys: %d, Sequences: %d",
                deviceCache.size(), keyCache.size(), hexKeyCache.size(), sequenceCache.size());
    }

    /**
     * Clear expired sequence cache entries
     */
    public void clearExpiredSequences() {
        sequenceCache.entrySet().removeIf(entry -> entry.getValue().isExpired(SEQUENCE_CACHE_TTL));

        if (logger.isDebugEnabled()) {
            logger.debug("Expired sequence cache entries cleared. Remaining: {}", sequenceCache.size());
        }
    }
}
