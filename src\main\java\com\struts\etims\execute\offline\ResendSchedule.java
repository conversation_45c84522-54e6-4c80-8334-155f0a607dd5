package com.struts.etims.execute.offline;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.manage.DataResendManager;

@Component
public class ResendSchedule {
    private static final Logger log = LoggerFactory.getLogger(ResendSchedule.class);

    @Autowired
    DataResendManager dataResendManager;

    @Autowired
    CheckApiConnection checkConnection;

    public static String getEbmTransData() throws Exception {
        return VsdcUtil.createEbmTransPath("AppData/EbmData");
    }

    @Scheduled(initialDelay = 1000L, fixedDelay = 30000L)
    public void executeResend() throws Exception {
        String filePathString = null;
        List<String> subFolderList = null;
        try {
            String filesPath = getEbmTransData();
            Path path = Paths.get(filesPath, new String[0]);
            Stream<Path> subpath = Files.list(path);
            subFolderList = (List<String>) subpath
                    .filter(x$0 -> Files.isDirectory(x$0, new java.nio.file.LinkOption[0])).map(Objects::toString)
                    .collect(Collectors.toList());
            for (String filePath : subFolderList) {
                File file = (new File(filePath)).getAbsoluteFile();
                String tinBhfPath = file.getName();
                try {
                    filePathString = DataResendManager.getDataReSendPath(tinBhfPath, "trnsSales");
                } catch (Exception exception) {
                }
                Path resendPath = Paths.get(filePathString, new String[0]);
                try {
                    if (!VsdcUtil.isEmpty(resendPath))
                        if (this.checkConnection.isConnected())
                            try {
                                this.dataResendManager.loadReSendFile("trnsSales", tinBhfPath);
                                this.dataResendManager.loadReSendStockFile("stockMaster", tinBhfPath);
                                this.dataResendManager.loadReSendStockIOFile("stockIO", tinBhfPath);
                                this.dataResendManager.loadReSendItemFile("saveItem", tinBhfPath);
                                this.dataResendManager.loadReSendImportsFile("trnsImports", tinBhfPath);
                                this.dataResendManager.loadReSendPurchaseFile("trnsPurchase", tinBhfPath);
                            } catch (IOException e) {
                                e.printStackTrace();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
