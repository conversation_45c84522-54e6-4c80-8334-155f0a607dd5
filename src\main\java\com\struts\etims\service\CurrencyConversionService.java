package com.struts.etims.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.struts.etims.entity.CurrencyConversion;
import com.struts.etims.model.ApiLayerResponse;
import com.struts.etims.model.BTCExchangeRate;
import com.struts.etims.model.BTCExchangeRates;
import com.struts.etims.model.CurrencyConversionList;
import com.struts.etims.model.Pagination;
import com.struts.etims.provider.APILayer;
import com.struts.etims.provider.BlockChainTicker;
import com.struts.etims.repo.CurrencyConversionRepository;
import com.struts.etims.utils.DateUtils;

@Service
public class CurrencyConversionService {

    private static final Logger logger = LoggerFactory.getLogger(CurrencyConversionService.class);

    @Autowired
    private CurrencyConversionRepository currencyConversionRepo;

    @Autowired
    private APILayer apilayer;

    @Autowired
    private BlockChainTicker blockChainTicker;

    @Autowired
    private DateUtils datelUtil;

    public CurrencyConversion convertCurrency(String from, String to, float amount) {

        String currencyPair = (from + to).toUpperCase();
        logger.info("looking up exchange rate by currency pair: " + currencyPair);

        CurrencyConversion currencyConversion = currencyConversionRepo.findByCurrencyPair(currencyPair);

        boolean canCallAPI = false;

        Date timeNow = new Date();

        if (currencyConversion != null) {
            // Check last updated at
            // if more than 24 hrs - call api and update exchange rate
            Long updateTimeDifference = datelUtil.getDifferenceBetweenDates(timeNow,
                    currencyConversion.getUpdatedAt());

            if (updateTimeDifference >= 1) {
                canCallAPI = true;
            }

        } else {
            // Save currency pair to db
            currencyConversion = new CurrencyConversion();
            currencyConversion.setCurrencyPair(currencyPair);
            currencyConversion.setRate(1);
            currencyConversionRepo.save(currencyConversion);
            canCallAPI = true;
        }

        if (canCallAPI) {
            // Call api directly
            ApiLayerResponse apiLayerResponse = apilayer.convertCurrency(from, to, amount);

            // save data to db
            currencyConversion.setRate(apiLayerResponse.getResult());
            currencyConversion.setCreatedAt(timeNow);
            currencyConversion.setUpdatedAt(timeNow);
            currencyConversionRepo.save(currencyConversion);
        }

        // Save reflection currency, e.g KESUSD from USDKES
        saveReflectionCurrency(from, to, currencyConversion);

        // Check amount requested, if amount > 1, multiply amount in response.
        if (amount > 1) {
            double convertedAmount = currencyConversion.getRate() * amount;
            currencyConversion.setConvertedAmount(convertedAmount);
        } else {
            currencyConversion.setConvertedAmount(1.00);
        }

        return currencyConversion;
    }

    public BTCExchangeRates fetchLatestBTCExchangeRates() {
        BTCExchangeRates btcExchangeRates = blockChainTicker.fetchBTCExchangeRates();

        // Fetch latest BTC Exchange rates and save them to db
        List<CurrencyConversion> btcCurrecyPairs = new ArrayList<>();
        Date timeNow = new Date();

        // Check if currency pairs are existing in DB
        List<String> currencyPairs = new ArrayList<>();
        for (Map.Entry<String, BTCExchangeRate> entry : btcExchangeRates.getRates().entrySet()) {
            String currencyCode = entry.getKey();
            String currencyPair = "BTC" + currencyCode.toUpperCase();
            currencyPairs.add(currencyPair);
        }

        // Retrieve existing currency pairs
        List<CurrencyConversion> currencyConversions = currencyConversionRepo.findAllByCurrencyPairIn(currencyPairs);

        // Store currency conversions in a map for retrieve with currencyPair as key
        List<String> existingCurrencyPairs = new ArrayList<>();
        Map<String, CurrencyConversion> curreyncyPairMap = new HashMap<>();
        for (CurrencyConversion currencyConversion : currencyConversions) {
            String currencyPair = currencyConversion.getCurrencyPair();
            existingCurrencyPairs.add(currencyPair);
            curreyncyPairMap.put(currencyPair, currencyConversion);
        }

        // Iterate over received btch exchange rates from external api and save data
        for (Map.Entry<String, BTCExchangeRate> entry : btcExchangeRates.getRates().entrySet()) {
            String currencyCode = entry.getKey();
            BTCExchangeRate rate = entry.getValue();
            String currencyPair = "BTC" + currencyCode.toUpperCase();

            CurrencyConversion currencyConversion = new CurrencyConversion();

            if (existingCurrencyPairs.contains(currencyPair)) {
                currencyConversion = curreyncyPairMap.get(currencyPair);
            }

            currencyConversion.setConvertedAmount(1.00);
            currencyConversion.setCurrencyPair(currencyPair);
            currencyConversion.setRate(rate.buy);
            currencyConversion.setCreatedAt(timeNow);
            currencyConversion.setUpdatedAt(timeNow);
            btcCurrecyPairs.add(currencyConversion);
        }

        currencyConversionRepo.saveAll(btcCurrecyPairs);

        return btcExchangeRates;
    }

    public void fetchLatestExchangeRates() {
        logger.info("fetching latest exchange rates..");

        String from = "USD";
        String to = "KES";
        String currencyPair = from + to;

        CurrencyConversion currencyConversion = currencyConversionRepo.findByCurrencyPair(currencyPair);

        ApiLayerResponse apiLayerResponse = apilayer.convertCurrency(from, to, 1);

        Date timeNow = new Date();
        currencyConversion.setRate(apiLayerResponse.getResult());
        currencyConversion.setCreatedAt(timeNow);
        currencyConversion.setUpdatedAt(timeNow);
        currencyConversionRepo.save(currencyConversion);

        saveReflectionCurrency(from, to, currencyConversion);
    }

    public CurrencyConversionList filterCurrencies() {
        CurrencyConversionList currencyList = new CurrencyConversionList();
        List<CurrencyConversion> currencyConversions = currencyConversionRepo.findAll(Sort.by("id").descending());
        currencyList.setCurrencyConversions(currencyConversions);

        Pagination pagination = new Pagination(1, 20);
        Long count = currencyConversionRepo.count();
        pagination.setCount(count);
        currencyList.setPagination(pagination);
        return currencyList;
    }

    public CurrencyConversion findByCurrencyPair(String currencyPair) {
        logger.info("looking up exchange rate by currency pair: " + currencyPair);
        CurrencyConversion currencyConversion = currencyConversionRepo.findByCurrencyPair(currencyPair);

        apilayer.convertCurrency("USD", "KES", 1);

        // Check last updated at
        // if more than 24 hrs - call api and update exchange rate
        return currencyConversion;
    }

    public CurrencyConversion getByID(long id) {
        return currencyConversionRepo.findById(id).get();
    }

    public void saveReflectionCurrency(
            String from,
            String to,
            CurrencyConversion currencyConversion) {

        String currencyPair = (to + from).toUpperCase();
        logger.info("saving reflection pair: " + currencyPair);

        CurrencyConversion reflectionCurrencyConversion = currencyConversionRepo.findByCurrencyPair(currencyPair);

        if (reflectionCurrencyConversion == null) {
            reflectionCurrencyConversion = new CurrencyConversion();
            reflectionCurrencyConversion.setCurrencyPair(currencyPair);
        }

        Double exchangeRate = 1 / currencyConversion.getRate();
        exchangeRate = Double.parseDouble(String.format("%.6f", exchangeRate));

        Date timeNow = new Date();
        reflectionCurrencyConversion.setConvertedAmount(1.00);
        reflectionCurrencyConversion.setRate(exchangeRate);
        reflectionCurrencyConversion.setCreatedAt(timeNow);
        reflectionCurrencyConversion.setUpdatedAt(timeNow);
        currencyConversionRepo.save(reflectionCurrencyConversion);
    }
}
