package com.struts.etims.service;

import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.struts.etims.execute.TrnsSalesExcute;

/**
 * Service for monitoring performance and managing caches
 */
@Service
public class PerformanceMonitoringService {
    
    private static final Logger logger = LoggerFactory.getLogger(PerformanceMonitoringService.class);
    
    @Autowired
    private TrnsSalesExcute trnsSalesExecute;
    
    // Performance counters
    private final AtomicLong totalTransactions = new AtomicLong(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    
    /**
     * Record transaction processing time for performance monitoring
     */
    public void recordTransactionTime(long processingTimeMs) {
        totalTransactions.incrementAndGet();
        totalProcessingTime.addAndGet(processingTimeMs);
    }
    
    /**
     * Get average processing time per transaction
     */
    public double getAverageProcessingTime() {
        long transactions = totalTransactions.get();
        if (transactions == 0) {
            return 0.0;
        }
        return (double) totalProcessingTime.get() / transactions;
    }
    
    /**
     * Get total number of processed transactions
     */
    public long getTotalTransactions() {
        return totalTransactions.get();
    }
    
    /**
     * Reset performance counters
     */
    public void resetCounters() {
        totalTransactions.set(0);
        totalProcessingTime.set(0);
        logger.info("Performance counters reset");
    }
    
    /**
     * Scheduled task to clear expired cache entries every 5 minutes
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void clearExpiredCaches() {
        try {
            trnsSalesExecute.clearExpiredSequences();
            logger.debug("Expired cache entries cleared automatically");
        } catch (Exception e) {
            logger.warn("Error clearing expired caches: {}", e.getMessage());
        }
    }
    
    /**
     * Scheduled task to log performance statistics every 15 minutes
     */
    @Scheduled(fixedRate = 900000) // 15 minutes
    public void logPerformanceStatistics() {
        try {
            long transactions = getTotalTransactions();
            double avgTime = getAverageProcessingTime();
            String cacheStats = trnsSalesExecute.getCacheStatistics();
            
            logger.info("Performance Stats - Transactions: {}, Avg Time: {:.2f}ms, {}", 
                       transactions, avgTime, cacheStats);
        } catch (Exception e) {
            logger.warn("Error logging performance statistics: {}", e.getMessage());
        }
    }
    
    /**
     * Scheduled task to clear all caches every 4 hours to prevent memory leaks
     */
    @Scheduled(fixedRate = 14400000) // 4 hours
    public void periodicCacheClear() {
        try {
            trnsSalesExecute.clearAllCaches();
            logger.info("Periodic cache clear completed");
        } catch (Exception e) {
            logger.warn("Error during periodic cache clear: {}", e.getMessage());
        }
    }
    
    /**
     * Get comprehensive performance report
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== ETIMS Performance Report ===\n");
        report.append("Total Transactions: ").append(getTotalTransactions()).append("\n");
        report.append("Average Processing Time: ").append(String.format("%.2f ms", getAverageProcessingTime())).append("\n");
        report.append("Cache Statistics: ").append(trnsSalesExecute.getCacheStatistics()).append("\n");
        report.append("================================");
        return report.toString();
    }
}
