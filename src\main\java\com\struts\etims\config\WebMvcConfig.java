package com.struts.etims.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedMethods("*")
                .allowedHeaders("*")
                .allowedOrigins("http://localhost:3000", "http://localhost:8051", "https://lm.strutstechnology.com",
                        "https://lmapi.strutstechnology.com")
                .allowCredentials(false)
                .exposedHeaders("X-STRUTS-TOKEN")
                .maxAge(-1);
    }
}