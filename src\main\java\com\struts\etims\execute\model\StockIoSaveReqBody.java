package com.struts.etims.execute.model;

import java.math.BigDecimal;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockIoSaveReqBody {
    private Integer sarNo;

    private Integer orgSarNo;

    private String regTyCd;

    private String custTin;

    private String custNm;

    private String custBhfId;

    private String sarTyCd;

    private String ocrnDt;

    private int totItemCnt;

    private BigDecimal totTaxblAmt;

    private BigDecimal totTaxAmt;

    private BigDecimal totAmt;

    private String remark;

    private String regrId;

    private String regrNm;

    private String modrId;

    private String modrNm;

    private List<StockIoItemSaveReq> itemList;
}
