package com.struts.etims.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.struts.etims.entity.Client;
import com.struts.etims.entity.License;
import com.struts.etims.model.DashboardAnalytics;
import com.struts.etims.repo.ClientRepository;
import com.struts.etims.repo.LicenseRepository;
import com.struts.etims.repo.UserRepository;
import com.struts.etims.utils.DateUtils;
import com.struts.etims.utils.SendEmail;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AnalyticsService {

    private static final Logger logger = LoggerFactory.getLogger(AnalyticsService.class);

    @Autowired
    private ClientRepository clientRepo;

    @Autowired
    private LicenseRepository licenseRepo;

    @Autowired
    private UserRepository userRepo;

    @Autowired
    private DateUtils datelUtil;

    @Autowired
    private SendEmail emailUtil;

    public DashboardAnalytics getDashboardAnalytics(boolean sendEmail) {
        DashboardAnalytics dashboardAnalytics = new DashboardAnalytics();

        Long clientsCount = clientRepo.countActiveClients();
        dashboardAnalytics.setClientsCount(clientsCount);

        Long licensesCount = licenseRepo.countValidLicences();
        dashboardAnalytics.setLicensesCount(licensesCount);

        List<License> licensesAboutToExpireList = licenseRepo.findLicensesAboutToExpire();

        Long licencesAboutToExpireCount = (long) licensesAboutToExpireList.size();
        dashboardAnalytics.setLicenesNearingExpiryCount(licencesAboutToExpireCount);

        // Fetch clients - for which the licenses are about to expire
        List<Client> clientList = getClientExpiryList(licensesAboutToExpireList);

        Map<Long, License> expiringLicensesMap = new HashMap<>();
        for (License license : licensesAboutToExpireList) {
            expiringLicensesMap.put(license.getClient_id(), license);
        }

        Date dateToday = new Date();

        // Loop over clients whose licenses are about to expire
        for (Client client : clientList) {
            License clientLicense = expiringLicensesMap.get(client.getId());

            // Calculate remaining license days
            Long licenseDaysRemaining = datelUtil.getDifferenceBetweenDates(dateToday, clientLicense.getExpires_at());
            clientLicense.setLicenseDaysRemaining(licenseDaysRemaining);
            client.setLicenseDaysRemaining(licenseDaysRemaining);
        }

        dashboardAnalytics.setExpiringLicenses(licensesAboutToExpireList);
        dashboardAnalytics.setClientExpiryList(clientList);

        Long usersCount = userRepo.count();
        dashboardAnalytics.setUsersCount(usersCount);

        List<License> expiredLicenses = licenseRepo.findExpiredLicenses();
        Long expiredLicensesCount = (long) expiredLicenses.size();
        dashboardAnalytics.setExpiredLicensesCount(expiredLicensesCount);

        if (sendEmail) {
            // Send email
            logger.info("Sending email analytics...");
            emailUtil.sendAnalyticsEmailWithTemplate(dashboardAnalytics);
        }

        return dashboardAnalytics;
    }

    public List<Client> getClientExpiryList(List<License> licensesAboutToExpire) {

        List<Long> clientIDs = new ArrayList<>();

        // Loop over findLicensesAboutToExpire
        for (License license : licensesAboutToExpire) {
            // get client ids
            clientIDs.add(license.getClient_id());
        }

        // fetch clients by ids
        List<Client> clientList = clientRepo.findAllByIdIn(clientIDs);
        return clientList;
    }
}
