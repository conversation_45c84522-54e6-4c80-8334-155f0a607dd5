package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;

@RestController
@RequestMapping({ "/main" })
public class MainExcute {

    @Autowired
    ApiClient apiClient;

    @GetMapping({ "/selectServerTime" })
    public String selectServerTime() {
        String resStr = null;
        try {
            resStr = this.apiClient.getClient("selectServerTime", null);
        } catch (Exception e) {
            resStr = "";
        }
        return resStr;
    }
}
