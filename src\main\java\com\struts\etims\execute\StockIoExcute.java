package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.StockIoSaveReq;
import com.struts.etims.execute.model.StockIoSaveReqBody;
import com.struts.etims.execute.model.StockIoSaveRes;
import com.struts.etims.execute.model.StockMoveReq;
import com.struts.etims.execute.model.StockMoveReqBody;
import com.struts.etims.execute.model.StockMoveRes;
import com.struts.etims.manage.DataResendManager;

@RestController
@RequestMapping({ "/stock" })
public class StockIoExcute {

    @Autowired
    ApiClient apiClient;

    @Autowired
    DataResendManager resendMng;

    @PostMapping({ "/selectStockItems" })
    public StockMoveRes selectStockMoveList(@RequestBody StockMoveReq req) {
        String resStr = null;
        StockMoveRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_STOCK_MOVE_SEARCH, "selectStockMoveList",
                StockMoveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (StockMoveRes) VsdcUtil.jsonToObject(resStr, StockMoveRes.class);
        } catch (Exception e) {
            res = new StockMoveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/saveStockItems" })
    public StockIoSaveRes insertStockIO(@RequestBody StockIoSaveReq req) {
        String resStr = null;
        StockIoSaveRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_STOCK_IO_SAVE, "insertStockIO",
                StockIoSaveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (StockIoSaveRes) VsdcUtil.jsonToObject(resStr, StockIoSaveRes.class);
            String reSendId = "stockIO_" + VsdcUtil.getDate("yyyyMMddHHmmss");
            String tinBhfPath = req.getTin() + "_" + req.getBhfId();
            if ("896".equals(res.getResultCd()) || "894".equals(res.getResultCd())) {
                String reqJson = VsdcUtil.objectToJson(req);
                this.resendMng.saveReSendFile("stockIO", reSendId, reqJson, tinBhfPath);
            }
        } catch (Exception e) {
            res = new StockIoSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
