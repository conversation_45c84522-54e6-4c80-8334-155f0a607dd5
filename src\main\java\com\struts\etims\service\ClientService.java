package com.struts.etims.service;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.struts.etims.entity.Client;
import com.struts.etims.entity.ClientApiKey;
import com.struts.etims.model.ClientList;
import com.struts.etims.model.Pagination;
import com.struts.etims.repo.ClientRepository;

@Service
public class ClientService {

    @Autowired
    private ClientRepository clientRepository;

    public Client deleteClient(Long id) {
        Client existingClient = clientRepository.findById(id).get();
        if (existingClient == null) {
            return null;
        }

        Date timeNow = new Date();
        existingClient.setDeletedAt(timeNow);
        clientRepository.save(existingClient);

        return existingClient;
    }

    public Client getByID(long id) {
        return clientRepository.findById(id).get();
    }

    public Client findByName(String email) {
        return clientRepository.findByName(email);
    }

    public ClientList filterClients() {
        ClientList clientList = new ClientList();
        List<Client> clients = clientRepository.findAllActiveClients();
        clientList.setClients(clients);

        Pagination pagination = new Pagination(1, 20);
        Long clientsCount = clientRepository.countActiveClients();
        pagination.setCount(clientsCount);
        clientList.setPagination(pagination);
        return clientList;
    }

    public Client save(Client Client) {
        Client existingClient = clientRepository.findByName(Client.getName());
        if (existingClient == null) {
            return clientRepository.save(Client);
        }

        return existingClient;
    }

    public Client updateClient(Client client, long clientID) {
        Client existingClient = clientRepository.findById(clientID).get();
        if (existingClient == null) {
            return null;
        }

        if (client.getEmail() != null) {
            existingClient.setEmail(client.getEmail());
        }

        if (client.getContact_person() != null) {
            existingClient.setContact_person(client.getContact_person());
        }

        if (client.getName() != null) {
            existingClient.setName(client.getName());
        }

        if (client.getDescription() != null) {
            existingClient.setDescription(client.getDescription());
        }

        if (client.getCountry_code() != null) {
            existingClient.setCountry_code(client.getCountry_code());
        }

        if (client.getLocation() != null) {
            existingClient.setLocation(client.getLocation());
        }

        return clientRepository.save(existingClient);
    }

}
