package com.struts.etims.config.manager;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.struts.etims.config.util.AesEnc;
import com.struts.etims.config.util.Base32;
import com.struts.etims.config.util.Base32ToHex;
import com.struts.etims.config.util.SkmmUtil;

/**
 * Optimized version of InternalManager with enhanced caching and performance improvements
 */
public class OptimizedInternalManager {
    
    // Pre-initialized converters for better performance
    private static final Base32ToHex BASE32_CONVERTER = new Base32ToHex();
    private static final Base32 BASE32_HMAC = new Base32();
    
    // Cache for hex keys to avoid repeated conversions
    private final Map<String, String> hexKeyCache = new ConcurrentHashMap<>();
    
    // Cache for AesEnc instances to avoid repeated object creation
    private final Map<String, AesEnc> aesEncCache = new ConcurrentHashMap<>();
    
    /**
     * Optimized version of getInternalData with caching and performance improvements
     */
    public String getInternalData(String salesTyCd, String rcptTyCd, double taxTyBTaxAmt, 
                                long rptNo, long totInvcNoCnt, String intrlKey) throws Exception {
        
        // Pre-calculate values to avoid multiple conversions
        int intNSTaxAmtB = 0;
        int intNRTaxAmtB = 0;
        
        if ("N".equals(salesTyCd)) {
            if ("S".equals(rcptTyCd)) {
                intNSTaxAmtB = (int) taxTyBTaxAmt;
            } else {
                intNRTaxAmtB = (int) taxTyBTaxAmt;
            }
        }
        
        int intTaxTyBTaxAmt = (int) Math.floor(taxTyBTaxAmt);
        intNRTaxAmtB += intTaxTyBTaxAmt;
        
        // Use StringBuilder with initial capacity for better performance
        StringBuilder sb = new StringBuilder(32);
        sb.append(AesEnc.longToHex(intNSTaxAmtB, 5));
        sb.append(AesEnc.longToHex(intNRTaxAmtB, 5));
        sb.append(AesEnc.longToHex(rptNo, 2));
        sb.append(AesEnc.longToHex(totInvcNoCnt, 4));
        
        // Get cached hex key or compute it
        String hexKey = getCachedHexKey(intrlKey);
        
        // Get cached AesEnc instance or create new one
        AesEnc internal = aesEncCache.computeIfAbsent(hexKey, k -> new AesEnc());
        
        return internal.encrypt(sb.toString(), hexKey);
    }
    
    /**
     * Optimized version of getSignature with caching and performance improvements
     */
    public String getSignature(String rcptDt, String tin, String custTin, long invcNo, 
                             double taxTyBTaxblAmt, double taxTyBTaxAmt, double taxTyATaxblAmt, 
                             double taxTyATaxAmt, String salesTyCd, String rcptTyCd, 
                             long totInvcNoCnt, long salesTyTotInvcNoCnt, String mrcNo, 
                             String sdcId, String signKey) throws Exception {
        
        // Use StringBuilder with estimated capacity for better performance
        StringBuilder sb = new StringBuilder(256);
        
        // Pre-format amounts to avoid repeated string operations
        String formattedTaxTyBTaxblAmt = SkmmUtil.lpadAmount(String.format("%.2f", taxTyBTaxblAmt), 15);
        String formattedTaxTyBTaxAmt = SkmmUtil.lpadAmount(String.format("%.2f", taxTyBTaxAmt), 15);
        String formattedTaxTyATaxblAmt = SkmmUtil.lpadAmount(String.format("%.2f", taxTyATaxblAmt), 15);
        String formattedTaxTyATaxAmt = SkmmUtil.lpadAmount(String.format("%.2f", taxTyATaxAmt), 15);
        
        // Build signature string efficiently
        sb.append(rcptDt)
          .append(tin)
          .append(custTin != null ? custTin : "")
          .append(SkmmUtil.lpad(String.valueOf(invcNo), 10))
          .append(mrcNo)
          .append(formattedTaxTyBTaxblAmt)
          .append(formattedTaxTyBTaxAmt)
          .append(formattedTaxTyATaxblAmt)
          .append(formattedTaxTyATaxAmt)
          .append(SkmmUtil.lpadAmount("0,00", 15))
          .append(SkmmUtil.lpadAmount("0,00", 15))
          .append(SkmmUtil.lpadAmount("0,00", 15))
          .append(SkmmUtil.lpadAmount("0,00", 15))
          .append(SkmmUtil.lpadAmount("0,00", 15))
          .append(SkmmUtil.lpadAmount("0,00", 15))
          .append(salesTyCd)
          .append(rcptTyCd)
          .append(sdcId)
          .append(rcptDt)
          .append(SkmmUtil.lpad(String.valueOf(totInvcNoCnt), 10))
          .append(SkmmUtil.lpad(String.valueOf(salesTyTotInvcNoCnt), 10));
        
        // Use pre-initialized Base32 instance for HMAC
        return BASE32_HMAC.hmacSha1(sb.toString(), signKey);
    }
    
    /**
     * Get cached hex key or compute it if not in cache
     * 
     * @param intrlKey The internal key to convert
     * @return Hex representation of the key
     */
    private String getCachedHexKey(String intrlKey) {
        return hexKeyCache.computeIfAbsent(intrlKey, key -> {
            byte[] base32DecodedData = BASE32_CONVERTER.decode(key);
            return BASE32_CONVERTER.convertToHex(base32DecodedData).toUpperCase();
        });
    }
    
    /**
     * Clear caches to prevent memory leaks in long-running applications
     */
    public void clearCaches() {
        hexKeyCache.clear();
        aesEncCache.clear();
    }
    
    /**
     * Get cache statistics for monitoring
     */
    public String getCacheStats() {
        return String.format("HexKeyCache: %d entries, AesEncCache: %d entries", 
                           hexKeyCache.size(), aesEncCache.size());
    }
}
