package com.struts.etims.execute.model;

import java.math.BigDecimal;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrnsPurchaseSaveReqBody {
    private Long invcNo;

    private Long orgInvcNo;

    private String taxprNm;

    private String spplrTin;

    private String spplrBhfId;

    private String spplrNm;

    private Long spplrInvcNo;

    private String regTyCd;

    private String pchsTyCd;

    private String rcptTyCd;

    private String pmtTyCd;

    private String pchsSttsCd;

    private String cfmDt;

    private String pchsDt;

    private String wrhsDt;

    private String cnclReqDt;

    private String cnclDt;

    private String rfdDt;

    private Integer totItemCnt;

    private BigDecimal taxblAmtA;

    private BigDecimal taxblAmtB;

    private BigDecimal taxblAmtC;

    private BigDecimal taxblAmtD;

    private BigDecimal taxblAmtE;

    private BigDecimal taxRtA;

    private BigDecimal taxRtB;

    private BigDecimal taxRtC;

    private BigDecimal taxRtD;

    private BigDecimal taxRtE;

    private BigDecimal taxAmtA;

    private BigDecimal taxAmtB;

    private BigDecimal taxAmtC;

    private BigDecimal taxAmtD;

    private BigDecimal taxAmtE;

    private BigDecimal totTaxblAmt;

    private BigDecimal totTaxAmt;

    private BigDecimal totAmt;

    private String remark;

    private String regrId;

    private String regrNm;

    private String modrId;

    private String modrNm;

    private List<TrnsPurchaseSaveItem> itemList;
}
