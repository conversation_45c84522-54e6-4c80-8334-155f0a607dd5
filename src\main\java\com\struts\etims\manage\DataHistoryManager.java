package com.struts.etims.manage;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import org.springframework.stereotype.Component;

import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.model.BaseRes;
import com.struts.etims.config.security.DataEncoder;
import com.struts.etims.config.util.VsdcUtil;

@Component
public class DataHistoryManager {
    private static String getDataHistoryPath(String tinBhfPath, String[] folderPath) throws Exception {
        return VsdcUtil.createFilePath(
                "AppData/EbmData" + File.separator + tinBhfPath + File.separator + "Data/history", folderPath);
    }

    public static void saveHistoryFile(ApiClientArg arg, String resExCd, String reqJson, String resJson,
            String tinBhfPath) {
        try {
            if (arg.getFolerPath() != null && (arg.getFolerPath()).length > 0) {
                String filePath = null;
                String[] folderPath = new String[(arg.getFolerPath()).length + 3];
                for (int i = 0; i < (arg.getFolerPath()).length; i++)
                    folderPath[i] = arg.getFolerPath()[i];
                folderPath[folderPath.length - 3] = VsdcUtil.getDate("yyyy");
                folderPath[folderPath.length - 2] = VsdcUtil.getDate("MM");
                folderPath[folderPath.length - 1] = VsdcUtil.getDate("dd");
                filePath = getDataHistoryPath(tinBhfPath, folderPath);
                String resultCd = "";
                try {
                    BaseRes resObj = (BaseRes) VsdcUtil.jsonToObject(resJson, BaseRes.class);
                    if (resObj != null && resObj.getResultCd() != null && !"".equals(resObj.getResultCd()))
                        resultCd = resObj.getResultCd();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                String fileName = arg.getUri() + "_" + VsdcUtil.getDate("yyyyMMddHHmmss") + "_" + resultCd;
                String saveJson = "{ \"URI\" : \"" + arg.getUri() + "\", \"REQUEST\" : " + reqJson + ", \"RESPONSE\" : "
                        + resJson + "}";
                String targetFile = filePath + File.separator + fileName + ".json";
                Writer wr = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(targetFile), "UTF-8"));
                wr.write(DataEncoder.encrypt(saveJson));
                wr.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
