package com.struts.etims.utils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import org.springframework.stereotype.Service;

@Service
public class DateUtils {

    public Long getDifferenceBetweenDates(Date date1, Date date2) {
        LocalDate localDate1 = convertToLocalDate(date1);
        LocalDate localDate2 = convertToLocalDate(date2);
        long daysDifference = ChronoUnit.DAYS.between(localDate1, localDate2);
        return daysDifference;
    }

    public LocalDate convertToLocalDate(Date date) {
        // Convert java.util.Date to LocalDate
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
}
