package com.struts.etims.service;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.struts.etims.entity.ClientApiKey;
import com.struts.etims.model.ClientApiKeyList;
import com.struts.etims.model.Pagination;
import com.struts.etims.repo.ClientApiKeyRepository;

@Service
public class ClientApiKeyService {

    @Autowired
    ClientApiKeyRepository clientApiKeyRepository;

    public ClientApiKey deleteClientApiKey(Long id) {
        ClientApiKey existingClientApiKey = clientApiKeyRepository.findById(id).get();
        if (existingClientApiKey == null) {
            return null;
        }

        Date timeNow = new Date();
        existingClientApiKey.setDeletedAt(timeNow);
        clientApiKeyRepository.save(existingClientApiKey);

        return existingClientApiKey;
    }

    public ClientApiKeyList filterClientApiKeys() {
        ClientApiKeyList clientApiKeyList = new ClientApiKeyList();
        List<ClientApiKey> clientApiKeys = clientApiKeyRepository.findAll(Sort.by("id").descending());
        clientApiKeyList.setClientApiKeys(clientApiKeys);

        Pagination pagination = new Pagination(1, 20);
        Long count = clientApiKeyRepository.count();
        pagination.setCount(count);
        clientApiKeyList.setPagination(pagination);
        return clientApiKeyList;
    }

    public ClientApiKey save(ClientApiKey apiKey) {
        if (apiKey != null) {
            return clientApiKeyRepository.save(apiKey);
        } else {
            return new ClientApiKey();
        }
    }
}
