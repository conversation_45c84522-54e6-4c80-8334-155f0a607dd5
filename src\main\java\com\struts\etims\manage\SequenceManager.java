package com.struts.etims.manage;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.struts.etims.config.security.AesCoder;
import com.struts.etims.config.util.SkmmUtil;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.InitializeVsdcExcute;
import com.struts.etims.execute.model.InitInfoReq;
import com.struts.etims.execute.model.TrnsSalesSaveWrRes;
import com.struts.etims.execute.offline.CheckApiConnection;

@Component
public class SequenceManager {

    // @Autowired
    // DataResendManager resendMng;

    // @Autowired
    // InitializeVsdcExcute initVsdc;

    // private static AesCoder aesCoder;

    private static CheckApiConnection checkConn;

    // @Autowired
    // public SequenceManager(AesCoder aesCoder) {
    // SequenceManager.aesCoder = aesCoder;
    // }

    public SequenceManager(CheckApiConnection checkConn) {
        SequenceManager.checkConn = checkConn;
    }

    public static void setRcptNo(Long rcptNo, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String plainReceipt = String.valueOf(rcptNo.longValue());
        String encryRcpt = AesCoder.encrypt(plainReceipt);
        Files.write(Paths.get(filePath + File.separator + "rcptNo", new String[0]), encryRcpt.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static void setInvcNo(Long invcNo, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String plainReceipt = String.valueOf(invcNo.longValue());
        String encryRcpt = AesCoder.encrypt(plainReceipt);
        Files.write(Paths.get(filePath + File.separator + "invcNo", new String[0]), encryRcpt.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static void setInvcNoNsr(Long invcNo, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String plainReceipt = String.valueOf(invcNo.longValue());
        String encryRcpt = AesCoder.encrypt(plainReceipt);
        Files.write(Paths.get(filePath + File.separator + "serlInvNsr", new String[0]), encryRcpt.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static void setInvcNoTsr(Long invcNo, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String plainReceipt = String.valueOf(invcNo.longValue());
        String encryRcpt = AesCoder.encrypt(plainReceipt);
        Files.write(Paths.get(filePath + File.separator + "serlInvTsr", new String[0]), encryRcpt.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static void setInvcNoPs(Long invcNo, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String plainReceipt = String.valueOf(invcNo.longValue());
        String encryRcpt = AesCoder.encrypt(plainReceipt);
        Files.write(Paths.get(filePath + File.separator + "serlInvPs", new String[0]), encryRcpt.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static void setInvcNoCsr(Long invcNo, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String plainReceipt = String.valueOf(invcNo.longValue());
        String encryRcpt = AesCoder.encrypt(plainReceipt);
        Files.write(Paths.get(filePath + File.separator + "serlInvCsr", new String[0]), encryRcpt.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static Long getRcptNo(String tin, String bhf, String dvcSrlNo, String tinBhfPath) throws Exception {
        DataResendManager resendMng = new DataResendManager();
        InitializeVsdcExcute initVsdc = new InitializeVsdcExcute();
        InitInfoReq req = null;
        Long rcptNo = null;
        String decryRcpt = null;
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String rcptNoTemp = new String(
                Files.readAllBytes(Paths.get(filePath + File.separator + "rcptNo", new String[0])));
        try {
            decryRcpt = AesCoder.decrypt(rcptNoTemp);
        } catch (Exception e) {
            req = new InitInfoReq();
            req.setTin(tin);
            req.setBhfId(bhf);
            req.setDvcSrlNo(dvcSrlNo);
            if (checkConn.isConnected())
                try {
                    resendMng.loadReSendFile("trnsSales", tinBhfPath);
                    initVsdc.selectTotReceiptNo(req);
                    rcptNo = getRcptNo(tin, bhf, dvcSrlNo, tinBhfPath);
                    return rcptNo;
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
        }
        if (decryRcpt != null && !"".equals(decryRcpt)) {
            rcptNo = Long.valueOf(decryRcpt);
            Long long_ = rcptNo;
            rcptNo = Long.valueOf(rcptNo.longValue() + 1L);
        }
        return rcptNo;
    }

    public static Long getRcptTypNo(String tin, String bhf, String dvcSrlNo, String invRcptKind, String tinBhfPath)
            throws Exception {
        DataResendManager resendMng = new DataResendManager();
        InitializeVsdcExcute initVsdc = new InitializeVsdcExcute();
        InitInfoReq req = null;
        Long rcptTpNo = null;
        String decryRcpt = null;
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String rcptNoTemp = new String(
                Files.readAllBytes(Paths.get(filePath + File.separator + invRcptKind, new String[0])));
        try {
            decryRcpt = AesCoder.decrypt(rcptNoTemp);
        } catch (Exception e) {
            req = new InitInfoReq();
            req.setTin(tin);
            req.setBhfId(bhf);
            req.setDvcSrlNo(dvcSrlNo);
            if (checkConn.isConnected())
                try {
                    resendMng.loadReSendFile("trnsSales", tinBhfPath);
                    initVsdc.selectTotReceiptNo(req);
                    rcptTpNo = getRcptTypNo(tin, bhf, dvcSrlNo, invRcptKind, tinBhfPath);
                    return rcptTpNo;
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
        }
        if (decryRcpt != null && !"".equals(decryRcpt)) {
            rcptTpNo = Long.valueOf(decryRcpt);
            Long long_ = rcptTpNo;
            rcptTpNo = Long.valueOf(rcptTpNo.longValue() + 1L);
        }
        return rcptTpNo;
    }

    public static Long getInvNo(String tin, String bhf, String dvcSrlNo, String tinBhfPath) throws Exception {
        DataResendManager resendMng = new DataResendManager();
        InitializeVsdcExcute initVsdc = new InitializeVsdcExcute();
        InitInfoReq req = null;
        Long invNo = null;
        String decryRcpt = null;
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String rcptNoTemp = new String(
                Files.readAllBytes(Paths.get(filePath + File.separator + "invcNo", new String[0])));
        try {
            decryRcpt = AesCoder.decrypt(rcptNoTemp);
        } catch (Exception e) {
            req = new InitInfoReq();
            req.setTin(tin);
            req.setBhfId(bhf);
            req.setDvcSrlNo(dvcSrlNo);
            if (checkConn.isConnected())
                try {
                    resendMng.loadReSendFile("trnsSales", tinBhfPath);
                    initVsdc.selectTotReceiptNo(req);
                    invNo = getInvNo(tin, bhf, dvcSrlNo, tinBhfPath);
                    return invNo;
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
        }
        if (decryRcpt != null && !"".equals(decryRcpt)) {
            invNo = Long.valueOf(decryRcpt);
            Long long_ = invNo;
            invNo = Long.valueOf(invNo.longValue() + 1L);
        }
        return invNo;
    }

    public static void setBhfOpenDt(String bhfOpenDt, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String encryBhfDt = AesCoder.encrypt(bhfOpenDt);
        Files.write(Paths.get(filePath + File.separator + "bhfOpenDt", new String[0]), encryBhfDt.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static Long getRptNo(String tinBhfPath) throws Exception {
        Long rptNo = null;
        TrnsSalesSaveWrRes res = null;
        String bhfOpenDt = null;
        String filePath = SkmmUtil.getEbmSquencePath(tinBhfPath);
        String encyptRptNo = new String(
                Files.readAllBytes(Paths.get(filePath + File.separator + "bhfOpenDt", new String[0])));
        try {
            bhfOpenDt = AesCoder.decrypt(encyptRptNo);
        } catch (Exception e) {
            e.printStackTrace();
            res = new TrnsSalesSaveWrRes();
            res.setResultCd("836");
            res.setResultMsg("Your Sequences have been altered, Connect to KRA API to get Sequences.");
        }
        if (bhfOpenDt != null && !"".equals(bhfOpenDt)) {
            String curDt = VsdcUtil.getDate("yyyyMMdd");
            rptNo = Long.valueOf(Long.valueOf(VsdcUtil.calDateBetweenAandB(curDt, bhfOpenDt)).longValue() + 1L);
        }
        return rptNo;
    }
}
