package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.TestEchoReq;
import com.struts.etims.execute.model.TestEchoReqBody;
import com.struts.etims.execute.model.TestEchoRes;

@RestController
@RequestMapping({ "/test" })
public class TestExcute {

    @Autowired
    ApiClient apiClient;

    @PostMapping({ "/echoTest" })
    public TestEchoRes selectTestEcho(@RequestBody TestEchoReq req) {
        String resStr = null;
        TestEchoRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_TEST_ECHO, "selectTestEcho",
                TestEchoReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            // System.out.println("echo test resStr=" + resStr);
            res = (TestEchoRes) VsdcUtil.jsonToObject(resStr, TestEchoRes.class);
            return res;

        } catch (Exception e) {
            // System.out.println("echo test exception=" + e.getMessage());
            res = new TestEchoRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
            return res;
        }
    }
}
