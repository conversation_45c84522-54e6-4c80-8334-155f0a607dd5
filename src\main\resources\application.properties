spring.profiles.active=dev

logging.pattern.file=%d %p %c{1.} [%t] %m%n
# logging.pattern.console=%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n
# logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%t] %-5level %logger{36}.%M(%F:%L) - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} | %-5level | %logger{1.} %L | %msg%n


apilayer.apikey=********************************

server.port=8088

spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Enable H2 console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

#api.external.domain=https://etims-api.kra.go.ke/etims-api
#api.external.domain=https://etims-api-sbx.kra.go.ke/etims-api
# profile.service.url=https://etims-api-test.kra.go.ke/etims-api
spring.main.web-application-type=servlet

# api.external.domain=http://10.153.1.88:8080/etims-api/

api.external.domain=https://etims-api-sbx.kra.go.ke/etims-api
# api.external.domain=https://etims-api.kra.go.ke/etims-api

can.post.data.to.kra=true

resend.days= 100
