package com.struts.etims.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.LastModifiedDate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Entity
@Table(name = "etims_sales_items")
public class EtimsSaleItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long etimsSaveSaleTransactionID;

    private Integer itemSeq;

    private String itemCd;

    private String itemClsCd;

    private String itemNm;

    private String bcd;

    private String pkgUnitCd;

    private BigDecimal pkg;

    private String qtyUnitCd;

    private BigDecimal qty;

    private BigDecimal prc;

    private BigDecimal splyAmt;

    private BigDecimal dcRt;

    private BigDecimal dcAmt;

    private String isrccCd;

    private String isrccNm;

    private BigDecimal isrcRt;

    private BigDecimal isrcAmt;

    private String taxTyCd;

    private BigDecimal taxblAmt;

    private BigDecimal taxAmt;

    private BigDecimal totAmt;

    private String companyPIN;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
