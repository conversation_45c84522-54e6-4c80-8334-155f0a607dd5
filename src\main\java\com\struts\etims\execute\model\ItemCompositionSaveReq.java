package com.struts.etims.execute.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ItemCompositionSaveReq {
    private String tin;

    private String bhfId;

    private String itemCd;

    private String cpstItemCd;

    private BigDecimal cpstQty;

    private String regrId;

    private String regrNm;
}
