package com.struts.etims.config.security;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import javax.crypto.Cipher;

public class DataEncoder {
    private static final String RSA_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCkHAaqzFRNGXSvBmezp2U2ukn70b/6yIv6u/hxeVOpxcQq39RjePw1jh91k0ahBI8uy8AsBRkF/3+aNtK4ECyTgsMCkz7oiWTbNjLRRegxFNeZADVADDO4eLQZY68DTApWCG+UMBB9JU/Eir+ZD+L7ypSC61mw+/4hRrPpL6ncawIDAQAB";

    private static final int STR_SIZE = 50;

    public static String encrypt(String inputStr) throws Exception {
        String encryptedData = "";
        if (inputStr != null && !"".equals(inputStr)) {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] bytePublicKey = Base64.getDecoder().decode(
                    "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCkHAaqzFRNGXSvBmezp2U2ukn70b/6yIv6u/hxeVOpxcQq39RjePw1jh91k0ahBI8uy8AsBRkF/3+aNtK4ECyTgsMCkz7oiWTbNjLRRegxFNeZADVADDO4eLQZY68DTApWCG+UMBB9JU/Eir+ZD+L7ypSC61mw+/4hRrPpL6ncawIDAQAB"
                            .getBytes());
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(bytePublicKey);
            PublicKey publicKey = keyFactory.generatePublic(publicKeySpec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(1, publicKey);
            int encrytCnt = (int) Math.ceil(inputStr.length() / 50.0D);
            String partStr = null;
            byte[] byteEncryptedData = null;
            for (int i = 0; i < encrytCnt; i++) {
                if (i == encrytCnt - 1) {
                    partStr = inputStr.substring(i * 50, inputStr.length());
                } else {
                    partStr = inputStr.substring(i * 50, (i + 1) * 50);
                }
                byteEncryptedData = cipher.doFinal(partStr.getBytes("UTF-8"));
                if (i > 0)
                    encryptedData = encryptedData + "_";
                encryptedData = encryptedData + Base64.getEncoder().encodeToString(byteEncryptedData);
            }
        }
        return encryptedData;
    }
}
