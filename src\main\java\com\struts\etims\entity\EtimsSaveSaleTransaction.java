package com.struts.etims.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.LastModifiedDate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Entity
@Table(name = "etims_save_sales_transactions")
public class EtimsSaveSaleTransaction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String tin;

    private String bhfId;

    private Long invcNo;

    private Long orgInvcNo;

    private String custTin;

    private String custNm;

    private String salesTyCd;

    private String rcptTyCd;

    private String pmtTyCd;

    private String salesSttsCd;

    private String cfmDt;

    private String salesDt;

    private String stockRlsDt;

    private String cnclReqDt;

    private String cnclDt;

    private String rfdDt;

    private String rfdRsnCd;

    private Integer totItemCnt;

    private BigDecimal taxblAmtA;

    private BigDecimal taxblAmtB;

    private BigDecimal taxblAmtC;

    private BigDecimal taxblAmtD;

    private BigDecimal taxblAmtE;

    private BigDecimal taxRtA;

    private BigDecimal taxRtB;

    private BigDecimal taxRtC;

    private BigDecimal taxRtD;

    private BigDecimal taxRtE;

    private BigDecimal taxAmtA;

    private BigDecimal taxAmtB;

    private BigDecimal taxAmtC;

    private BigDecimal taxAmtD;

    private BigDecimal taxAmtE;

    private BigDecimal totTaxblAmt;

    private BigDecimal totTaxAmt;

    private BigDecimal totAmt;

    private String prchrAcptcYn;

    private String remark;

    private String regrId;

    private String regrNm;

    private String modrId;

    private String modrNm;

    // private EtimsSaveSaleReceipt receipt;

    // private List<TrnsSalesSaveWrItem> itemList;

    private String companyPIN;

    private String kraResultCode;

    private String kraSendStatus; // pending, sent, failed

    private String kraResponseMessage;

    // Signature data
    private String intrlData;

    private String mrcNo;

    private Long rcptNo;

    private String rcptSign;

    private String resultDt;

    private String sdcId;

    private Long totRcptNo;

    private String vsdcRcptPbctDate;

    private String invRcptKind;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "processing_status")
    private String processingStatus;

    @Column(name = "last_retry_time")
    private Date lastRetryTime;
}
