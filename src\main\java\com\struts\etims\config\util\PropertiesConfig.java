package com.struts.etims.config.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Configuration
@ConfigurationProperties
@PropertySource({ "classpath:application.properties" })
public class PropertiesConfig {
    @Value("${api.external.domain}")
    private String serverUrl;

    @Value("${resend.days}")
    private String resendPeriod;
}
