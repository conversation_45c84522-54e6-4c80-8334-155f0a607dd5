package com.struts.etims.config.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Properties;
import java.util.stream.Stream;

import org.springframework.beans.BeanUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

public class VsdcUtil {
    public static String getProperty(String key) throws Exception {
        String value = null;
        Properties props = new Properties();
        props.load(ClassLoader.getSystemResourceAsStream("application.properties"));
        value = props.getProperty(key);
        return value;
    }

    public static String objectToJson(Object reqObj) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(reqObj);
    }

    public static <T> T jsonToObject(String jsonStr, Class<T> objCls) throws Exception {
        return (T) (new ObjectMapper()).readValue(jsonStr, objCls);
    }

    public static void copyBean(Object dest, Object org) throws Exception {
        BeanUtils.copyProperties(dest, org);
    }

    public static String createFilePath(String defaultFilePath, String[] fileKinds) throws Exception {
        String filePath = System.getProperty("user.home") + File.separator + defaultFilePath;
        for (String fileKind : fileKinds)
            filePath = filePath + File.separator + fileKind;
        Path folderPath = Paths.get(filePath, new String[0]);
        if (!Files.exists(folderPath, new java.nio.file.LinkOption[0]))
            Files.createDirectories(folderPath, (FileAttribute<?>[]) new FileAttribute[0]);
        return filePath;
    }

    public static String createEbmTransPath(String defaultFilePath) throws Exception {
        String filePath = System.getProperty("user.home") + File.separator + defaultFilePath;
        Path folderPath = Paths.get(filePath, new String[0]);
        if (!Files.exists(folderPath, new java.nio.file.LinkOption[0]))
            Files.createDirectories(folderPath, (FileAttribute<?>[]) new FileAttribute[0]);
        return filePath;
    }

    public static String getDate(String dateFormat) {
        DateFormat df = new SimpleDateFormat(dateFormat);
        Calendar calendar = Calendar.getInstance();
        return df.format(calendar.getTime());
    }

    public static long calDateBetweenAandB(String fromDt, String toDt) throws Exception {
        String strFormat = "yyyyMMdd";
        long diffDay = 0L;
        SimpleDateFormat sdf = new SimpleDateFormat(strFormat);
        Date startDate = sdf.parse(fromDt);
        Date endDate = sdf.parse(toDt);
        diffDay = (startDate.getTime() - endDate.getTime()) / 86400000L;
        return diffDay;
    }

    public static String DateStringFormater(Long inputdate) {
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String strDate = dateFormat.format(inputdate);
        return strDate;
    }

    public static boolean isEmpty(Path path) throws IOException {
        if (Files.isDirectory(path, new java.nio.file.LinkOption[0])) {
            Stream<Path> entries = Files.list(path);
            try {
                boolean bool = !entries.findFirst().isPresent() ? true : false;
                if (entries != null)
                    entries.close();
                return bool;
            } catch (Throwable throwable) {
                if (entries != null)
                    try {
                        entries.close();
                    } catch (Throwable throwable1) {
                        throwable.addSuppressed(throwable1);
                    }
                throw throwable;
            }
        }
        return false;
    }
}
