package com.struts.etims.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.entity.Client;
import com.struts.etims.model.ClientList;
import com.struts.etims.service.ClientService;

@RestController
@RequestMapping("/clients")
public class ClientController {

    @Autowired
    private ClientService clientService;

    @PostMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Client createClient(@RequestBody Client Client) {
        return clientService.save(Client);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Client deleteClientByID(@PathVariable Long id) {
        return clientService.deleteClient(id);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Client findClientByID(@PathVariable int id) {
        return clientService.getByID(id);
    }

    @GetMapping("/by_name/{name}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Client findClientByName(@PathVariable String name) {
        return clientService.findByName(name);
    }

    @GetMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public ClientList filterClients() {
        return clientService.filterClients();
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Client updateClient(@RequestBody Client client, @PathVariable int id) {
        return clientService.updateClient(client, id);
    }
}
