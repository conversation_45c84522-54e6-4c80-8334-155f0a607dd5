package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.TrnsPurchaseSalesReq;
import com.struts.etims.execute.model.TrnsPurchaseSalesReqBody;
import com.struts.etims.execute.model.TrnsPurchaseSalesRes;
import com.struts.etims.execute.model.TrnsPurchaseSaveReq;
import com.struts.etims.execute.model.TrnsPurchaseSaveReqBody;
import com.struts.etims.execute.model.TrnsPurchaseSaveRes;

@RestController
@RequestMapping({ "/trnsPurchase" })
public class TrnsPurchaseExcute {

    @Autowired
    ApiClient apiClient;

    @PostMapping({ "/savePurchases" })
    public TrnsPurchaseSaveRes saveTrnsPurchase(@RequestBody TrnsPurchaseSaveReq req) {

        String resStr = null;
        TrnsPurchaseSaveRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_TRNS_PURCHASE_SAVE, "insertTrnsPurchase",
                TrnsPurchaseSaveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (TrnsPurchaseSaveRes) VsdcUtil.jsonToObject(resStr, TrnsPurchaseSaveRes.class);
        } catch (Exception e) {
            res = new TrnsPurchaseSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/selectTrnsPurchaseSales" })
    public TrnsPurchaseSalesRes selectTrnsPurchaseSalesList(@RequestBody TrnsPurchaseSalesReq req) {
        String resStr = null;
        TrnsPurchaseSalesRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_TRNS_PURCHASE_SALES_SEARCH,
                "selectTrnsPurchaseSalesList", TrnsPurchaseSalesReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (TrnsPurchaseSalesRes) VsdcUtil.jsonToObject(resStr, TrnsPurchaseSalesRes.class);
        } catch (Exception e) {
            res = new TrnsPurchaseSalesRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
