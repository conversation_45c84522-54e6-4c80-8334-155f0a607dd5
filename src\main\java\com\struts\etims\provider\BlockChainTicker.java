package com.struts.etims.provider;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.struts.etims.model.BTCExchangeRates;

@Service
public class BlockChainTicker {

    private final String apiUrl = "https://blockchain.info/ticker";

    private static final Logger logger = LoggerFactory.getLogger(BlockChainTicker.class);

    public BTCExchangeRates fetchBTCExchangeRates() {

        logger.info("retrieving latest BTC exchange rates...");

        ResponseEntity<BTCExchangeRates> responseEntity = new RestTemplate().getForEntity(apiUrl,
                BTCExchangeRates.class);

        return responseEntity.getBody();
    }

}
