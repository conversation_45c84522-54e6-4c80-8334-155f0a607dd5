package com.struts.etims.config.util;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Properties;

public class SkmmUtil {
    public static String getProperty(String key) throws Exception {
        String value = null;
        Properties props = new Properties();
        props.load(ClassLoader.getSystemResourceAsStream("ebm/skmm/config/skmmConfig.properties"));
        value = props.getProperty(key);
        return value;
    }

    public static String getEbmDevicePath(String tinBhfPath) throws Exception {
        return createFilePath("AppData/EbmData" + File.separator + tinBhfPath + File.separator + "Ebm/device");
    }

    public static String getEbmSquencePath(String tinBhfPath) throws Exception {
        return createFilePath("AppData/EbmData" + File.separator + tinBhfPath + File.separator + "Ebm/sequence");
    }

    private static String createFilePath(String fileKind) throws Exception {
        String filePath = System.getProperty("user.home") + File.separator + fileKind;
        Path folderPath = Paths.get(filePath, new String[0]);
        if (!Files.exists(folderPath, new java.nio.file.LinkOption[0]))
            Files.createDirectories(folderPath, (FileAttribute<?>[]) new FileAttribute[0]);
        return filePath;
    }

    public static String lpadAmount(String data, int strLen) {
        data = data.replace(".", ",");
        return lpad(data, strLen);
    }

    public static String lpad(String data, int strLen) {
        data = data.trim();
        int dataLen = data.length();
        if (dataLen == strLen || strLen == 0)
            return data;
        if (dataLen < strLen) {
            for (int i = 0; i < strLen - dataLen; i++)
                data = " " + data;
        } else {
            return "ERROR";
        }
        return data;
    }
}
