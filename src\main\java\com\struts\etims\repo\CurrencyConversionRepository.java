package com.struts.etims.repo;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.struts.etims.entity.CurrencyConversion;

public interface CurrencyConversionRepository extends JpaRepository<CurrencyConversion, Long> {

    CurrencyConversion findByCurrencyPair(String currencyPair);

    List<CurrencyConversion> findAllByCurrencyPairIn(List<String> currencyPairs);

    List<CurrencyConversion> findAllByIdIn(List<Long> currencyIds);
}
