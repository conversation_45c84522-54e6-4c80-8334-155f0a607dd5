package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.CustReq;
import com.struts.etims.execute.model.CustReqBody;
import com.struts.etims.execute.model.CustRes;

@RestController
@RequestMapping({ "/customers" })
public class CustomerExcute {

    @Autowired
    ApiClient apiClient;

    @PostMapping({ "/selectCustomer" })
    public CustRes selectCustomer(@RequestBody CustReq req) {
        String resStr = null;
        CustRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_CUST_SEARCH, "selectCustomer",
                CustReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (CustRes) VsdcUtil.jsonToObject(resStr, CustRes.class);
        } catch (Exception e) {
            res = new CustRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
