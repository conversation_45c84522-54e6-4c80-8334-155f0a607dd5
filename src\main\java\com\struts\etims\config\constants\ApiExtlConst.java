package com.struts.etims.config.constants;

public class ApiExtlConst {

    public static final String HEADER_TIN = "tin";

    public static final String HEADER_BHF_ID = "bhfId";

    public static final String HEADER_CMC_KEY = "cmcKey";

    public static final String URL_TEST_ECHO = "selectTestEcho";

    public static final String[] FOLDER_PATH_TEST_ECHO = new String[] { "test", "echo" };

    public static final String URL_MAIN_SERVERTIME = "selectServerTime";

    public static final String URL_INIT_INFO = "selectInitVsdcInfo";

    public static final String[] FOLDER_PATH_INIT_INFO = new String[] { "initInfo" };

    public static final String URL_INIT_INFO_SEQ = "selectInitInfoVsdcSeq";

    public static final String[] FOLDER_PATH_INIT_INFO_SEQ = new String[] { "initInfo" };

    public static final String URL_CODE_SEARCH = "selectCodeList";

    public static final String[] FOLDER_PATH_CODE_SEARCH = new String[] { "code", "search" };

    public static final String URL_ITEM_CLASS_SEARCH = "selectItemClsList";

    public static final String[] FOLDER_PATH_ITEM_CLASS_SEARCH = new String[] { "item", "class", "search" };

    public static final String URL_CUST_SEARCH = "selectCustomer";

    public static final String[] FOLDER_PATH_CUST_SEARCH = new String[] { "cust", "search" };

    public static final String URL_ITEM_SEARCH = "selectItemList";

    public static final String[] FOLDER_PATH_ITEM_SEARCH = new String[] { "item", "base", "search" };

    public static final String URL_ITEM_SAVE = "saveItem";

    public static final String[] FOLDER_PATH_ITEM_SAVE = new String[] { "item", "base", "save" };

    public static final String URL_ITEM_COMPOSITION_SAVE = "saveItemComposition";

    public static final String[] FOLDER_PATH_ITEM_COMPOSITION_SAVE = new String[] { "item", "composition", "save" };

    public static final String URL_BHF_SEARCH = "selectBhfList";

    public static final String[] FOLDER_PATH_BHF_SEARCH = new String[] { "bhf", "base", "search" };

    public static final String URL_BHF_USER_SAVE = "saveBhfUser";

    public static final String[] FOLDER_PATH_BHF_USER_SAVE = new String[] { "bhf", "user", "save" };

    public static final String URL_BHF_INSURANCE_SAVE = "saveBhfInsurance";

    public static final String[] FOLDER_PATH_BHF_INSURANCE_SAVE = new String[] { "bhf", "insurance", "save" };

    public static final String URL_BHF_CUST_SAVE = "saveBhfCustomer";

    public static final String[] FOLDER_PATH_BHF_CUST_SAVE = new String[] { "bhf", "cust", "save" };

    public static final String URL_TRNS_SALES_SAVES_VSDC = "saveTrnsSalesVsdc";

    public static final String[] FOLDER_PATH_TRNS_SALES_SAVE = new String[] { "trns", "sales", "base", "save" };

    public static final String URL_TRNS_PURCHASE_SALES_SEARCH = "selectTrnsPurchaseSalesList";

    public static final String[] FOLDER_PATH_TRNS_PURCHASE_SALES_SEARCH = new String[] { "trns", "purchase", "sales",
            "search" };

    public static final String URL_TRNS_PURCHASE_SAVE = "insertTrnsPurchase";

    public static final String[] FOLDER_PATH_TRNS_PURCHASE_SAVE = new String[] { "trns", "purchase", "base", "save" };

    public static final String URL_IMPORT_ITEM_SEARCH = "selectImportItemList";

    public static final String[] FOLDER_PATH_IMPORT_ITEM_SEARCH = new String[] { "import", "item", "search" };

    public static final String URL_IMPORT_ITEM_UPDATE = "updateImportItem";

    public static final String[] FOLDER_PATH_IMPORTITEM_UPDATE = new String[] { "import", "item", "update" };

    public static final String URL_STOCK_MASTER_SAVE = "saveStockMaster";

    public static final String[] FOLDER_PATH_STOCK_MASTER_SAVE = new String[] { "stock", "master", "save" };

    public static final String URL_STOCK_MOVE_SEARCH = "selectStockMoveList";

    public static final String[] FOLDER_PATH_STOCK_MOVE_SEARCH = new String[] { "stock", "move", "search" };

    public static final String URL_STOCK_IO_SAVE = "insertStockIO";

    public static final String[] FOLDER_PATH_STOCK_IO_SAVE = new String[] { "stock", "io", "save" };

    public static final String URL_NOTICE_SEARCH = "selectNoticeList";

    public static final String[] FOLDER_PATH_NOTICE_SEARCH = new String[] { "notice", "search" };

    public static final String URL_REPORT_Z_SAVE = "saveReportZ";

    public static final String[] FOLDER_PATH_REPORT_Z_SAVE = new String[] { "report", "z", "save" };

    public static final String URL_REPORT_Z_CHECK = "checkReportZ";

    public static final String[] FOLDER_PATH_REPORT_Z_CHECK = new String[] { "report", "z", "check" };
}
