package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.NoticeReq;
import com.struts.etims.execute.model.NoticeReqBody;
import com.struts.etims.execute.model.NoticeRes;

@RestController
@RequestMapping({ "/notices" })
public class NoticeExcute {

    @Autowired
    ApiClient apiClient;

    @PostMapping({ "/selectNotices" })
    public NoticeRes selectNoticeList(@RequestBody NoticeReq req) {
        String resStr = null;
        NoticeRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_NOTICE_SEARCH, "selectNoticeList",
                NoticeReqBody.class);
        try {
            resStr = apiClient.getClient(clientArg, req);
            // System.out.println("resStr=" + resStr);
            res = (NoticeRes) VsdcUtil.jsonToObject(resStr, NoticeRes.class);
        } catch (Exception e) {
            res = new NoticeRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
