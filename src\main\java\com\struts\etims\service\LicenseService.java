package com.struts.etims.service;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.struts.etims.entity.Client;
import com.struts.etims.entity.License;
import com.struts.etims.model.LicenseList;
import com.struts.etims.model.Pagination;
import com.struts.etims.repo.ClientRepository;
import com.struts.etims.repo.LicenseRepository;

@Service
public class LicenseService {

    @Autowired
    private ClientRepository clientRepo;

    @Autowired
    private LicenseRepository licenseRepository;

    public License deleteLicense(Long id) {
        License existingLicense = licenseRepository.findById(id).get();
        if (existingLicense == null) {
            return null;
        }

        Date timeNow = new Date();
        existingLicense.setDeletedAt(timeNow);
        licenseRepository.save(existingLicense);

        return existingLicense;
    }

    public License getByID(long id) {
        return licenseRepository.findById(id).get();
    }

    public LicenseList filterLicenses() {
        LicenseList licenseList = new LicenseList();
        List<License> licenses = licenseRepository.findAll(Sort.by("id").descending());
        licenseList.setLicenses(licenses);

        Pagination pagination = new Pagination(1, 20);
        Long licensesCount = licenseRepository.count();
        pagination.setCount(licensesCount);
        licenseList.setPagination(pagination);
        return licenseList;
    }

    public LicenseList filterLicensesAboutToExpire() {
        LicenseList licenseList = new LicenseList();
        List<License> licensesAboutToExpire = licenseRepository.findLicensesAboutToExpire();
        licenseList.setLicenses(licensesAboutToExpire);

        Pagination pagination = new Pagination(1, 20);
        Long licensesCount = Long.valueOf(licensesAboutToExpire.size());
        pagination.setCount(licensesCount);
        licenseList.setPagination(pagination);
        return licenseList;
    }

    public License save(License license) {
        // Confirm existence of client
        Client existingClient = clientRepo.findById(license.getClient_id()).get();
        if (existingClient == null) {
            return null;
        }

        // Soft delete any existing licenses that have expired
        licenseRepository.updateDeletedAtByClientId(existingClient.getId());

        return licenseRepository.save(license);
    }

    public License updateLicense(License license, long licenseID) {
        License existingLicense = licenseRepository.findById(licenseID).get();
        if (existingLicense == null) {
            return null;
        }

        existingLicense.setLicense(license.getLicense());

        return licenseRepository.save(existingLicense);
    }

}
