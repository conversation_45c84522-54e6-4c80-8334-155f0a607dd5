package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.ReportZCheckReq;
import com.struts.etims.execute.model.ReportZCheckRes;
import com.struts.etims.execute.model.ReportZSaveReq;
import com.struts.etims.execute.model.ReportZSaveReqBody;
import com.struts.etims.execute.model.ReportZSaveRes;
import com.struts.etims.execute.model.ReportZSaveUpdateReq;
import com.struts.etims.manage.DataZreportManager;

@RestController
@RequestMapping({ "/reports" })
public class ReportExcute {

    @Autowired
    ApiClient apiClient;

    @Autowired
    DataZreportManager dataZrptMgr;

    @PostMapping({ "/saveZReports" })
    public ReportZSaveRes saveReportZ(@RequestBody ReportZSaveUpdateReq req) {

        String resStr = null;
        ReportZSaveRes res = null;
        String tinBhfPath = req.getTin() + "_" + req.getBhfId();
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_REPORT_Z_SAVE, "saveReportZ",
                ReportZSaveReqBody.class);
        try {
            if (!DataZreportManager.isSendZReport(req.getRptDe(), tinBhfPath)) {
                ReportZSaveReq saveReq = DataZreportManager.createZReport(req.getRptDe(), tinBhfPath);
                if (saveReq == null) {
                    res = new ReportZSaveRes();
                    res.setResultCd("804");
                    res.setResultMsg("There is no data to send for the report.");
                } else {
                    resStr = this.apiClient.getClient(clientArg, saveReq);
                    res = (ReportZSaveRes) VsdcUtil.jsonToObject(resStr, ReportZSaveRes.class);
                    if ("000".equals(res.getResultCd()))
                        DataZreportManager.updateZReport(req.getRptDe(), tinBhfPath);
                }
            } else {
                res = new ReportZSaveRes();
                res.setResultCd("803");
                res.setResultMsg("This is a report that transfer is complete.");
            }
        } catch (Exception e) {
            res = new ReportZSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/checkZReport" })
    public ReportZCheckRes checkReportZ(@RequestBody ReportZCheckReq req) {
        String resStr = null;
        ReportZCheckRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_REPORT_Z_CHECK, "checkReportZ",
                ReportZSaveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (ReportZCheckRes) VsdcUtil.jsonToObject(resStr, ReportZCheckRes.class);
        } catch (Exception e) {
            res = new ReportZCheckRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
