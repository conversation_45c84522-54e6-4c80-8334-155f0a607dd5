package com.struts.etims;

import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.struts.etims.KRAEtimsVSCU;

@SpringBootApplication
@EnableScheduling
@EnableAsync
public class KRAEtimsVSCU {
	private static final Logger logger = LoggerFactory.getLogger(KRAEtimsVSCU.class);

	public static void main(String[] args) {
		TimeZone.setDefault(TimeZone.getTimeZone("Africa/Nairobi"));
		logger.info("Starting VsdcApplication...");
		SpringApplication.run(KRAEtimsVSCU.class, args);
		logger.info("VsdcApplication started.");
	}
}
