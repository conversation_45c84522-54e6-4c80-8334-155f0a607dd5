package com.struts.etims.entity;

import java.time.LocalDateTime;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.LastModifiedDate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Entity
@Table(name = "etims_save_sales_receipts")
public class EtimsSaveSaleReceipt {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long etimsSaveSaleTransactionID;

    private Long curRcptNo;

    private Long totRcptNo;

    private String custTin;

    private String custMblNo;

    private Long rptNo;

    private String rcptPbctDt;

    private String intrlData;

    private String rcptSign;

    private String jrnl;

    private String trdeNm;

    private String adrs;

    private String topMsg;

    private String btmMsg;

    private String prchrAcptcYn;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
