package com.struts.etims.config.util;

import java.util.Arrays;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class Base32 {
    private static final String base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";

    private static final int[] base32Lookup = new int[] {
            255, 255, 26, 27, 28, 29, 30, 31, 255, 255,
            255, 255, 255, 255, 255, 255, 255, 0, 1, 2,
            3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
            13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
            23, 24, 25, 255, 255, 255, 255, 255, 255, 0,
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
            11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
            21, 22, 23, 24, 25, 255, 255, 255, 255, 255 };

    public String hmacSha1(String value, String key) {
        try {
            byte[] keyBytes = decode(key);
            // System.out.println("------------- hmacSha1 value -----------------");
            // System.out.println(value);
            // System.out.println(key);
            // System.out.println("------------- Key Bytes -----------------");
            // System.out.println(Arrays.toString(keyBytes));

            SecretKeySpec signingKey = new SecretKeySpec(keyBytes, "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);

            byte[] rawHmac = mac.doFinal(value.getBytes());
            // System.out.println("------------- rawHmac -----------------");
            // System.out.println(Arrays.toString(rawHmac));

            byte[] encVal = new byte[10];
            System.arraycopy(rawHmac, 0, encVal, 0, 10);
            // System.out.println("------------- encVal -----------------");
            // System.out.println(Arrays.toString(encVal));

            String k = encode(encVal);
            // System.out.println(k);

            return k;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String encode(byte[] bytes) {
        int i = 0, index = 0, digit = 0;
        StringBuffer base32 = new StringBuffer((bytes.length + 7) * 8 / 5);
        while (i < bytes.length) {
            int currByte = (bytes[i] >= 0) ? bytes[i] : (bytes[i] + 256);
            if (index > 3) {
                int nextByte;
                if (i + 1 < bytes.length) {
                    nextByte = (bytes[i + 1] >= 0) ? bytes[i + 1] : (bytes[i + 1] + 256);
                } else {
                    nextByte = 0;
                }
                digit = currByte & 255 >> index;
                index = (index + 5) % 8;
                digit <<= index;
                digit |= nextByte >> 8 - index;
                i++;
            } else {
                digit = currByte >> 8 - index + 5 & 0x1F;
                index = (index + 5) % 8;
                if (index == 0)
                    i++;
            }
            base32.append("ABCDEFGHIJKLMNOPQRSTUVWXYZ234567".charAt(digit));
        }
        return base32.toString();
    }

    public static byte[] decode(String base32) {
        byte[] bytes = new byte[base32.length() * 5 / 8];
        for (int i = 0, index = 0, offset = 0; i < base32.length(); i++) {
            int lookup = base32.charAt(i) - 48;
            if (lookup >= 0 && lookup < base32Lookup.length) {
                int digit = base32Lookup[lookup];
                if (digit != 255)
                    if (index <= 3) {
                        index = (index + 5) % 8;
                        if (index == 0) {
                            bytes[offset] = (byte) (bytes[offset] | digit);
                            offset++;
                            if (offset >= bytes.length)
                                break;
                        } else {
                            bytes[offset] = (byte) (bytes[offset] | digit << 8 - index);
                        }
                    } else {
                        index = (index + 5) % 8;
                        bytes[offset] = (byte) (bytes[offset] | digit >>> index);
                        offset++;
                        if (offset >= bytes.length)
                            break;
                        bytes[offset] = (byte) (bytes[offset] | digit << 8 - index);
                    }
            }
        }
        return bytes;
    }

    public static void main(String[] args) {
        String d = "20240814013856P051922564NKRA00000179     20146 0,00           0,00           0,0018,00        2120,00         292,41 0,00           0,00           0,00 0,00           0,00           0,00NSKRACU030000002320240814013856     20146     20146";
        // System.out.println("leng:" + d.length());
        Base32 b = new Base32();
        String kkey = "933585492BAF400687782557741E1A6B99A90A8E9D4148BE8B29";
        System.out.println(" Original: " + b.hmacSha1(d, kkey));
        System.out.println(b.hmacSha1(d, kkey));
    }
}
