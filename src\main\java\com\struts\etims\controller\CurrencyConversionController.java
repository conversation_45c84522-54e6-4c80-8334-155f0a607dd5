package com.struts.etims.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.entity.CurrencyConversion;
import com.struts.etims.model.BTCExchangeRates;
import com.struts.etims.model.CurrencyConversionList;
import com.struts.etims.service.CurrencyConversionService;

@RestController
@RequestMapping("/currencies")
public class CurrencyConversionController {

    @Autowired
    private CurrencyConversionService currencyConversionService;

    @GetMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public CurrencyConversionList fetchCurrencies() {
        return currencyConversionService.filterCurrencies();
    }

    @GetMapping("/btc")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public BTCExchangeRates fetchLatestBTCExchangeRates() {
        return currencyConversionService.fetchLatestBTCExchangeRates();
    }

    // /convert?from=KES&to=USD&amount=1
    @GetMapping("/convert")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public CurrencyConversion convertCurrency(
            @RequestParam String from,
            @RequestParam String to,
            @RequestParam float amount) {

        return currencyConversionService.convertCurrency(from, to, amount);
    }
}
