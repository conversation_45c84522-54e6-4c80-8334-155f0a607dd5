package com.struts.etims.execute.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ItemSaveReq {
    private String tin;

    private String bhfId;

    private String itemCd;

    private String itemClsCd;

    private String itemTyCd;

    private String itemNm;

    private String itemStdNm;

    private String orgnNatCd;

    private String pkgUnitCd;

    private String qtyUnitCd;

    private String taxTyCd;

    private String btchNo;

    private String bcd;

    private BigDecimal dftPrc;

    private BigDecimal grpPrcL1;

    private BigDecimal grpPrcL2;

    private BigDecimal grpPrcL3;

    private BigDecimal grpPrcL4;

    private BigDecimal grpPrcL5;

    private String addInfo;

    private BigDecimal sftyQty;

    private String isrcAplcbYn;

    private String useYn;

    private String regrId;

    private String regrNm;

    private String modrId;

    private String modrNm;
}
