package com.struts.etims.execute.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportZSaveReqBody {
    private String rptDe;

    private String sdcId;

    private Long rptNo;

    private Integer rcptPbctCnt;

    private Long rcptOpnNo;

    private Long rcptClsNo;

    private Integer nrmRcptPbctCnt;

    private Long nrmRcptOpnNo;

    private Long nrmRcptClsNo;

    private BigDecimal nrmSalesAmt;

    private BigDecimal nrmRfdAmt;

    private BigDecimal nrmSalesTaxAmt;

    private BigDecimal nrmRfdTaxAmt;

    private Integer cpyRcptPbctCnt;

    private Long cpyRcptOpnNo;

    private Long cpyRcptClsNo;

    private BigDecimal cpySalesAmt;

    private BigDecimal cpyRfdAmt;

    private BigDecimal cpySalesTaxAmt;

    private BigDecimal cpyRfdTaxAmt;

    private Integer trnRcptPbctCnt;

    private Long trnRcptOpnNo;

    private Long trnRcptClsNo;

    private BigDecimal trnSalesAmt;

    private BigDecimal trnRfdAmt;

    private BigDecimal trnSalesTaxAmt;

    private BigDecimal trnRfdTaxAmt;

    private Integer pfmRcptPbctCnt;

    private Long pfmRcptOpnNo;

    private Long pfmRcptClsNo;

    private BigDecimal pfmSalesAmt;

    private BigDecimal pfmRfdAmt;

    private BigDecimal pfmSalesTaxAmt;

    private BigDecimal pfmRfdTaxAmt;

    private String regrId;

    private String regrNm;
}
