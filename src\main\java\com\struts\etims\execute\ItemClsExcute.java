package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.ItemClsReq;
import com.struts.etims.execute.model.ItemClsReqBody;
import com.struts.etims.execute.model.ItemClsRes;

@RestController
@RequestMapping({ "/itemClass" })
public class ItemClsExcute {

    @Autowired
    ApiClient apiClient;

    @PostMapping({ "/selectItemsClass" })
    public ItemClsRes selectItemClsList(@RequestBody ItemClsReq req) {
        String resStr = null;
        ItemClsRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_ITEM_CLASS_SEARCH, "selectItemClsList",
                ItemClsReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (ItemClsRes) VsdcUtil.jsonToObject(resStr, ItemClsRes.class);
        } catch (Exception e) {
            res = new ItemClsRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
