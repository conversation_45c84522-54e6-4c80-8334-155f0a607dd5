package com.struts.etims.execute.offline;

import java.net.HttpURLConnection;
import java.net.URL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.struts.etims.config.util.PropertiesConfig;

@Component
public class CheckApiConnection {

    @Autowired
    PropertiesConfig propConfig;

    public boolean isConnected() {
        boolean status = true;
        try {
            int timeOutInMilliSec = 3000;
            String serverUrl = this.propConfig.getServerUrl();
            URL url = new URL(serverUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("HEAD");
            conn.setConnectTimeout(timeOutInMilliSec);
            conn.setReadTimeout(timeOutInMilliSec);
            int responseCode = conn.getResponseCode();
            if (200 <= responseCode && responseCode <= 399)
                status = true;
        } catch (Exception ex) {
            status = false;
        }
        return status;
    }
}
