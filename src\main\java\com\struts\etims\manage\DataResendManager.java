package com.struts.etims.manage;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileAttribute;
import java.nio.file.attribute.FileTime;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.struts.etims.config.security.AesCoder;
import com.struts.etims.config.util.PropertiesConfig;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.ImportItemExcute;
import com.struts.etims.execute.ItemExcute;
import com.struts.etims.execute.StockIoExcute;
import com.struts.etims.execute.StockMasterExcute;
import com.struts.etims.execute.TrnsPurchaseExcute;
import com.struts.etims.execute.TrnsSalesExcute;
import com.struts.etims.execute.model.ImportItemUpdateReq;
import com.struts.etims.execute.model.ImportItemUpdateRes;
import com.struts.etims.execute.model.ItemSaveReq;
import com.struts.etims.execute.model.ItemSaveRes;
import com.struts.etims.execute.model.StockIoSaveReq;
import com.struts.etims.execute.model.StockIoSaveRes;
import com.struts.etims.execute.model.StockMasterSaveReq;
import com.struts.etims.execute.model.StockMasterSaveRes;
import com.struts.etims.execute.model.TrnsPurchaseSaveReq;
import com.struts.etims.execute.model.TrnsPurchaseSaveRes;
import com.struts.etims.execute.model.TrnsSalesSaveWrReq;
import com.struts.etims.execute.model.TrnsSalesSaveWrRes;

@Component
public class DataResendManager {
    private static final Logger log = LoggerFactory.getLogger(DataResendManager.class);

    @Autowired
    PropertiesConfig propConfig;

    @Autowired
    ImportItemExcute imports;

    @Autowired
    TrnsPurchaseExcute purchase;

    @Autowired
    AesCoder aesCoder;

    public static String getDataReSendPath(String tinBhfPath, String reSendKind) throws Exception {
        return VsdcUtil.createFilePath("AppData/EbmData" + File.separator + tinBhfPath + File.separator + "Data/resend",
                new String[] { reSendKind });
    }

    private static String getProcessedDataPath(String tinBhfPath, String reSendKind) throws Exception {
        return VsdcUtil.createFilePath(
                "AppData/EbmData" + File.separator + tinBhfPath + File.separator + "Data/processed",
                new String[] { reSendKind + File.separator });
    }

    public void saveReSendFile(String reSendKind, String reSendId, String reqJson, String tinBhfPath) throws Exception {
        String filePath = getDataReSendPath(tinBhfPath, reSendKind);
        Path folderPath = Paths.get(filePath, new String[0]);
        if (!Files.exists(folderPath, new java.nio.file.LinkOption[0]))
            Files.createDirectories(folderPath, (FileAttribute<?>[]) new FileAttribute[0]);
        String targetFile = filePath + File.separator + reSendId + ".json";
        Writer wr = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(targetFile), "UTF-8"));
        wr.write(AesCoder.encrypt(reqJson));
        wr.close();
    }

    public void loadReSendFile(String reSendKind, String tinBhfPath) throws Exception, IOException {
        TrnsSalesSaveWrReq wrReq = null;
        TrnsSalesSaveWrRes res = null;
        String filePath = getDataReSendPath(tinBhfPath, reSendKind);
        String processedPath = getProcessedDataPath(tinBhfPath, reSendKind);
        String jsonStr = null;
        Stream<Path> walk = Files.walk(Paths.get(filePath, new String[0]), new java.nio.file.FileVisitOption[0]);
        try {
            TrnsSalesExcute exec = new TrnsSalesExcute();
            List<String> result = (List<String>) walk.map(x -> x.toString()).filter(f -> f.contains(".json"))
                    .collect(Collectors.toList());
            for (String filePathList : result) {
                File file = (new File(filePathList)).getAbsoluteFile();
                jsonStr = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
                Path createPath = Paths.get(processedPath + file.toPath().getFileName(), new String[0]);
                try {
                    jsonStr = AesCoder.decrypt(jsonStr);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (jsonStr != null && !"".equals(jsonStr)) {
                    try {
                        wrReq = (TrnsSalesSaveWrReq) VsdcUtil.jsonToObject(jsonStr, TrnsSalesSaveWrReq.class);
                        res = exec.saveTrnsSales(wrReq, true);
                        if (!"896".equals(res.getResultCd()) && !"894".equals(res.getResultCd()))
                            Files.move(file.toPath(), createPath,
                                    new CopyOption[] { StandardCopyOption.REPLACE_EXISTING });
                    } catch (Exception e) {
                        res = new TrnsSalesSaveWrRes();
                        res.setResultCd("899");
                        res.setResultMsg("An error regarding Client occurred.");
                    }
                    continue;
                }
                res = new TrnsSalesSaveWrRes();
                res.setResultCd("801");
                res.setResultMsg("There is no data to retransmit.");
            }
            if (walk != null)
                walk.close();
        } catch (Throwable throwable) {
            if (walk != null)
                try {
                    walk.close();
                } catch (Throwable throwable1) {
                    throwable.addSuppressed(throwable1);
                }
            throw throwable;
        }
    }

    public static void removeReSendFile(String reSendKind, String reSendId, String tinBhfPath) {
        try {
            String filePath = getDataReSendPath(tinBhfPath, reSendKind) + File.separator + reSendId + ".json";
            Files.delete(Paths.get(filePath, new String[0]));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isSend(String reSendKind, String tinBhfPath) throws Exception {
        boolean result = true;
        String pattern = "yyyyMMdd";
        String curDt = VsdcUtil.getDate(pattern);
        long resendDays = Long.valueOf(this.propConfig.getResendPeriod()).longValue();
        File resendFolder = new File(getDataReSendPath(tinBhfPath, reSendKind));
        if (resendFolder.isDirectory())
            for (File file : resendFolder.listFiles()) {
                BasicFileAttributes attr = Files.readAttributes(file.toPath(), BasicFileAttributes.class,
                        new java.nio.file.LinkOption[0]);
                FileTime time = attr.creationTime();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
                String resendDt = simpleDateFormat.format(new Date(time.toMillis()));
                long calDays = VsdcUtil.calDateBetweenAandB(curDt, resendDt);
                if (resendDays < calDays) {
                    result = false;
                    break;
                }
            }
        return result;
    }

    public void loadReSendStockFile(String reSendKind, String tinBhfPath) throws Exception, IOException {
        StockMasterSaveReq wrReq = null;
        StockMasterSaveRes res = null;
        String filePath = getDataReSendPath(tinBhfPath, reSendKind);
        String processedPath = getProcessedDataPath(tinBhfPath, reSendKind);
        String jsonStr = null;
        Stream<Path> walk = Files.walk(Paths.get(filePath, new String[0]), new java.nio.file.FileVisitOption[0]);
        try {
            StockMasterExcute stock = new StockMasterExcute();
            List<String> result = (List<String>) walk.map(x -> x.toString()).filter(f -> f.contains(".json"))
                    .collect(Collectors.toList());
            for (String filePathList : result) {
                File file = (new File(filePathList)).getAbsoluteFile();
                jsonStr = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
                Path createPath = Paths.get(processedPath + file.toPath().getFileName(), new String[0]);
                try {
                    jsonStr = AesCoder.decrypt(jsonStr);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (jsonStr != null && !"".equals(jsonStr)) {
                    try {
                        wrReq = (StockMasterSaveReq) VsdcUtil.jsonToObject(jsonStr, StockMasterSaveReq.class);
                        res = stock.saveStockMaster(wrReq);
                        if (!"896".equals(res.getResultCd()) && !"894".equals(res.getResultCd()))
                            Files.move(file.toPath(), createPath,
                                    new CopyOption[] { StandardCopyOption.REPLACE_EXISTING });
                    } catch (Exception e) {
                        res = new StockMasterSaveRes();
                        res.setResultCd("899");
                        res.setResultMsg("An error regarding Client occurred.");
                    }
                    continue;
                }
                res = new StockMasterSaveRes();
                res.setResultCd("801");
                res.setResultMsg("There is no data to retransmit.");
            }
            if (walk != null)
                walk.close();
        } catch (Throwable throwable) {
            if (walk != null)
                try {
                    walk.close();
                } catch (Throwable throwable1) {
                    throwable.addSuppressed(throwable1);
                }
            throw throwable;
        }
    }

    public void loadReSendStockIOFile(String reSendKind, String tinBhfPath) throws Exception, IOException {
        StockIoSaveReq wrReq = null;
        StockIoSaveRes res = null;
        String filePath = getDataReSendPath(tinBhfPath, reSendKind);
        String processedPath = getProcessedDataPath(tinBhfPath, reSendKind);
        String jsonStr = null;
        Stream<Path> walk = Files.walk(Paths.get(filePath, new String[0]), new java.nio.file.FileVisitOption[0]);
        try {
            List<String> result = (List<String>) walk.map(x -> x.toString()).filter(f -> f.contains(".json"))
                    .collect(Collectors.toList());
            for (String filePathList : result) {
                File file = (new File(filePathList)).getAbsoluteFile();
                jsonStr = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
                Path createPath = Paths.get(processedPath + file.toPath().getFileName(), new String[0]);
                try {
                    jsonStr = AesCoder.decrypt(jsonStr);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (jsonStr != null && !"".equals(jsonStr)) {
                    try {
                        StockIoExcute stockio = new StockIoExcute();
                        wrReq = (StockIoSaveReq) VsdcUtil.jsonToObject(jsonStr, StockIoSaveReq.class);
                        res = stockio.insertStockIO(wrReq);
                        if (!"896".equals(res.getResultCd()) && !"894".equals(res.getResultCd()))
                            Files.move(file.toPath(), createPath,
                                    new CopyOption[] { StandardCopyOption.REPLACE_EXISTING });
                    } catch (Exception e) {
                        res = new StockIoSaveRes();
                        res.setResultCd("899");
                        res.setResultMsg("An error regarding Client occurred.");
                    }
                    continue;
                }
                res = new StockIoSaveRes();
                res.setResultCd("801");
                res.setResultMsg("There is no data to retransmit.");
            }
            if (walk != null)
                walk.close();
        } catch (Throwable throwable) {
            if (walk != null)
                try {
                    walk.close();
                } catch (Throwable throwable1) {
                    throwable.addSuppressed(throwable1);
                }
            throw throwable;
        }
    }

    public void loadReSendItemFile(String reSendKind, String tinBhfPath) throws Exception, IOException {
        ItemSaveReq wrReq = null;
        ItemSaveRes res = null;
        String filePath = getDataReSendPath(tinBhfPath, reSendKind);
        String processedPath = getProcessedDataPath(tinBhfPath, reSendKind);
        String jsonStr = null;
        Stream<Path> walk = Files.walk(Paths.get(filePath, new String[0]), new java.nio.file.FileVisitOption[0]);
        try {
            List<String> result = (List<String>) walk.map(x -> x.toString()).filter(f -> f.contains(".json"))
                    .collect(Collectors.toList());
            for (String filePathList : result) {
                File file = (new File(filePathList)).getAbsoluteFile();
                ItemExcute item = new ItemExcute();
                jsonStr = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
                Path createPath = Paths.get(processedPath + file.toPath().getFileName(), new String[0]);
                try {
                    jsonStr = AesCoder.decrypt(jsonStr);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (jsonStr != null && !"".equals(jsonStr)) {
                    try {
                        wrReq = (ItemSaveReq) VsdcUtil.jsonToObject(jsonStr, ItemSaveReq.class);
                        res = item.saveItem(wrReq);
                        if (!"896".equals(res.getResultCd()) && !"894".equals(res.getResultCd()))
                            Files.move(file.toPath(), createPath,
                                    new CopyOption[] { StandardCopyOption.REPLACE_EXISTING });
                    } catch (Exception e) {
                        res = new ItemSaveRes();
                        res.setResultCd("899");
                        res.setResultMsg("An error regarding Client occurred.");
                    }
                    continue;
                }
                res = new ItemSaveRes();
                res.setResultCd("801");
                res.setResultMsg("There is no data to retransmit.");
            }
            if (walk != null)
                walk.close();
        } catch (Throwable throwable) {
            if (walk != null)
                try {
                    walk.close();
                } catch (Throwable throwable1) {
                    throwable.addSuppressed(throwable1);
                }
            throw throwable;
        }
    }

    public void loadReSendPurchaseFile(String reSendKind, String tinBhfPath) throws Exception, IOException {
        TrnsPurchaseSaveReq wrReq = null;
        TrnsPurchaseSaveRes res = null;
        String filePath = getDataReSendPath(tinBhfPath, reSendKind);
        String processedPath = getProcessedDataPath(tinBhfPath, reSendKind);
        String jsonStr = null;
        Stream<Path> walk = Files.walk(Paths.get(filePath, new String[0]), new java.nio.file.FileVisitOption[0]);
        try {
            List<String> result = (List<String>) walk.map(x -> x.toString()).filter(f -> f.contains(".json"))
                    .collect(Collectors.toList());
            for (String filePathList : result) {
                File file = (new File(filePathList)).getAbsoluteFile();
                jsonStr = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
                Path createPath = Paths.get(processedPath + file.toPath().getFileName(), new String[0]);
                try {
                    jsonStr = AesCoder.decrypt(jsonStr);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (jsonStr != null && !"".equals(jsonStr)) {
                    try {
                        wrReq = (TrnsPurchaseSaveReq) VsdcUtil.jsonToObject(jsonStr, TrnsPurchaseSaveReq.class);
                        res = this.purchase.saveTrnsPurchase(wrReq);
                        if (!"896".equals(res.getResultCd()) && !"894".equals(res.getResultCd()))
                            Files.move(file.toPath(), createPath,
                                    new CopyOption[] { StandardCopyOption.REPLACE_EXISTING });
                    } catch (Exception e) {
                        res = new TrnsPurchaseSaveRes();
                        res.setResultCd("899");
                        res.setResultMsg("An error regarding Client occurred.");
                    }
                    continue;
                }
                res = new TrnsPurchaseSaveRes();
                res.setResultCd("801");
                res.setResultMsg("There is no data to retransmit.");
            }
            if (walk != null)
                walk.close();
        } catch (Throwable throwable) {
            if (walk != null)
                try {
                    walk.close();
                } catch (Throwable throwable1) {
                    throwable.addSuppressed(throwable1);
                }
            throw throwable;
        }
    }

    public void loadReSendImportsFile(String reSendKind, String tinBhfPath) throws Exception, IOException {
        ImportItemUpdateReq wrReq = null;
        ImportItemUpdateRes res = null;
        String filePath = getDataReSendPath(tinBhfPath, reSendKind);
        String processedPath = getProcessedDataPath(tinBhfPath, reSendKind);
        String jsonStr = null;
        Stream<Path> walk = Files.walk(Paths.get(filePath, new String[0]), new java.nio.file.FileVisitOption[0]);
        try {
            List<String> result = (List<String>) walk.map(x -> x.toString()).filter(f -> f.contains(".json"))
                    .collect(Collectors.toList());
            for (String filePathList : result) {
                File file = (new File(filePathList)).getAbsoluteFile();
                jsonStr = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
                Path createPath = Paths.get(processedPath + file.toPath().getFileName(), new String[0]);
                try {
                    jsonStr = AesCoder.decrypt(jsonStr);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (jsonStr != null && !"".equals(jsonStr)) {
                    try {
                        wrReq = (ImportItemUpdateReq) VsdcUtil.jsonToObject(jsonStr, ImportItemUpdateReq.class);
                        res = this.imports.updateImportItem(wrReq);
                        if (!"896".equals(res.getResultCd()) && !"894".equals(res.getResultCd()))
                            Files.move(file.toPath(), createPath,
                                    new CopyOption[] { StandardCopyOption.REPLACE_EXISTING });
                    } catch (Exception e) {
                        res = new ImportItemUpdateRes();
                        res.setResultCd("899");
                        res.setResultMsg("An error regarding Client occurred.");
                    }
                    continue;
                }
                res = new ImportItemUpdateRes();
                res.setResultCd("801");
                res.setResultMsg("There is no data to retransmit.");
            }
            if (walk != null)
                walk.close();
        } catch (Throwable throwable) {
            if (walk != null)
                try {
                    walk.close();
                } catch (Throwable throwable1) {
                    throwable.addSuppressed(throwable1);
                }
            throw throwable;
        }
    }
}
