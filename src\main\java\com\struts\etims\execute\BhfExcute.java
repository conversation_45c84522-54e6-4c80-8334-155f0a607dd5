package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.BhfCustSaveReq;
import com.struts.etims.execute.model.BhfCustSaveReqBody;
import com.struts.etims.execute.model.BhfCustSaveRes;
import com.struts.etims.execute.model.BhfInsuranceSaveReq;
import com.struts.etims.execute.model.BhfInsuranceSaveReqBody;
import com.struts.etims.execute.model.BhfInsuranceSaveRes;
import com.struts.etims.execute.model.BhfReq;
import com.struts.etims.execute.model.BhfReqBody;
import com.struts.etims.execute.model.BhfRes;
import com.struts.etims.execute.model.BhfUserSaveReq;
import com.struts.etims.execute.model.BhfUserSaveReqBody;
import com.struts.etims.execute.model.BhfUserSaveRes;

@RestController
@RequestMapping({ "/branches" })
public class BhfExcute {

    @Autowired
    ApiClient apiClient;

    @PostMapping({ "/selectBranches" })
    public BhfRes selectBhfbList(@RequestBody BhfReq req) {
        String resStr = null;
        BhfRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_BHF_SEARCH, "selectBhfList",
                BhfReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (BhfRes) VsdcUtil.jsonToObject(resStr, BhfRes.class);
        } catch (Exception e) {
            res = new BhfRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/saveBrancheUsers" })
    public BhfUserSaveRes saveBhfUser(@RequestBody BhfUserSaveReq req) {
        String resStr = null;
        BhfUserSaveRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_BHF_USER_SAVE, "saveBhfUser",
                BhfUserSaveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (BhfUserSaveRes) VsdcUtil.jsonToObject(resStr, BhfUserSaveRes.class);
        } catch (Exception e) {
            res = new BhfUserSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/saveBrancheInsurances" })
    public BhfInsuranceSaveRes saveBhfInsurance(@RequestBody BhfInsuranceSaveReq req) {
        String resStr = null;
        BhfInsuranceSaveRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_BHF_INSURANCE_SAVE, "saveBhfInsurance",
                BhfInsuranceSaveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (BhfInsuranceSaveRes) VsdcUtil.jsonToObject(resStr, BhfInsuranceSaveRes.class);
        } catch (Exception e) {
            res = new BhfInsuranceSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }

    @PostMapping({ "/saveBrancheCustomers" })
    public BhfCustSaveRes saveBhfCustomer(@RequestBody BhfCustSaveReq req) {
        String resStr = null;
        BhfCustSaveRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_BHF_CUST_SAVE, "saveBhfCustomer",
                BhfCustSaveReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (BhfCustSaveRes) VsdcUtil.jsonToObject(resStr, BhfCustSaveRes.class);
        } catch (Exception e) {
            res = new BhfCustSaveRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
