package com.struts.etims.execute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.config.client.ApiClient;
import com.struts.etims.config.client.ApiClientArg;
import com.struts.etims.config.constants.ApiExtlConst;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.CodeReq;
import com.struts.etims.execute.model.CodeReqBody;
import com.struts.etims.execute.model.CodeRes;

@RestController
@RequestMapping({ "/code" })
public class CodeExcute {

    @Autowired
    ApiClient apiClient;

    @PostMapping({ "/selectCodes" })
    public CodeRes selectCodeList(@RequestBody CodeReq req) {
        String resStr = null;
        CodeRes res = null;
        ApiClientArg clientArg = new ApiClientArg(ApiExtlConst.FOLDER_PATH_CODE_SEARCH, "selectCodeList",
                CodeReqBody.class);
        try {
            resStr = this.apiClient.getClient(clientArg, req);
            res = (CodeRes) VsdcUtil.jsonToObject(resStr, CodeRes.class);
        } catch (Exception e) {
            res = new CodeRes();
            res.setResultCd("899");
            res.setResultMsg("An error regarding Client occurred.");
        }
        return res;
    }
}
