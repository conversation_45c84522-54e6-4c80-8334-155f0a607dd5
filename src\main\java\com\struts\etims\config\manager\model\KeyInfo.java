package com.struts.etims.config.manager.model;

import java.io.Serializable;

public class KeyInfo implements Serializable {
    private static final long serialVersionUID = -7747426194938062884L;

    private String cmcKey;

    private String intrlKey;

    private String signKey;

    public String getCmcKey() {
        return this.cmcKey;
    }

    public void setCmcKey(String cmcKey) {
        this.cmcKey = cmcKey;
    }

    public String getIntrlKey() {
        return this.intrlKey;
    }

    public void setIntrlKey(String intrlKey) {
        this.intrlKey = intrlKey;
    }

    public String getSignKey() {
        return this.signKey;
    }

    public void setSignKey(String signKey) {
        this.signKey = signKey;
    }

    public static long getSerialversionuid() {
        return -7747426194938062884L;
    }
}
