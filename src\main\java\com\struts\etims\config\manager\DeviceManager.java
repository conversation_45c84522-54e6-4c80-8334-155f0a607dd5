package com.struts.etims.config.manager;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.struts.etims.config.manager.model.KeyInfo;
import com.struts.etims.config.security.AesCoder;
import com.struts.etims.config.util.SkmmUtil;

@Component
public class DeviceManager {

    @Autowired
    private static AesCoder aesCoder;

    // public DeviceManager(AesCoder aesCoder) {
    // DeviceManager.aesCoder = aesCoder;
    // }

    public static void setKey(KeyInfo info, String tinBhfPath) throws Exception {
        String encCmcKey = null;
        String encIntrlKey = null;
        String encSignKey = null;
        String filePath = SkmmUtil.getEbmDevicePath(tinBhfPath);
        encCmcKey = aesCoder.encrypt(info.getCmcKey());
        Files.write(Paths.get(filePath + File.separator + "cmcKey", new String[0]), encCmcKey.getBytes(),
                new java.nio.file.OpenOption[0]);
        encIntrlKey = AesCoder.encrypt(info.getIntrlKey());
        Files.write(Paths.get(filePath + File.separator + "intrlKey", new String[0]), encIntrlKey.getBytes(),
                new java.nio.file.OpenOption[0]);
        encSignKey = AesCoder.encrypt(info.getSignKey());
        Files.write(Paths.get(filePath + File.separator + "signKey", new String[0]), encSignKey.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static KeyInfo getKeys(String tinBhfPath) throws Exception {
        KeyInfo info = null;
        String decCmcKey = null;
        String decIntrlKey = null;
        String decSignKey = null;
        String filePath = SkmmUtil.getEbmDevicePath(tinBhfPath);
        info = new KeyInfo();
        decCmcKey = new String(Files.readAllBytes(Paths.get(filePath + File.separator + "cmcKey", new String[0])));
        String decryptedCmcKey = AesCoder.decrypt(decCmcKey);
        // System.out.println("decryptedCmcKey: " + decryptedCmcKey);
        info.setCmcKey(decryptedCmcKey);
        decIntrlKey = new String(Files.readAllBytes(Paths.get(filePath + File.separator + "intrlKey", new String[0])));
        String decryptedIntrlKey = AesCoder.decrypt(decIntrlKey);
        // System.out.println("decryptedIntrlKey: " + decryptedIntrlKey);
        info.setIntrlKey(decryptedIntrlKey);
        decSignKey = new String(Files.readAllBytes(Paths.get(filePath + File.separator + "signKey", new String[0])));
        String decryptedSignKey = AesCoder.decrypt(decSignKey);
        // System.out.println("decryptedSignKey: " + decryptedSignKey);
        info.setSignKey(decryptedSignKey);
        return info;
    }

    public static String getKey(String keyKind, String tinBhfPath) throws Exception {
        String decKey = null;
        decKey = new String(Files.readAllBytes(
                Paths.get(SkmmUtil.getEbmDevicePath(tinBhfPath) + File.separator + keyKind, new String[0])));
        decKey = AesCoder.decrypt(decKey);
        return decKey;
    }

    public static void setSdcID(String sdcId, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmDevicePath(tinBhfPath);
        sdcId = AesCoder.encrypt(sdcId);
        Files.write(Paths.get(filePath + File.separator + "sdcId", new String[0]), sdcId.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static String getSdcID(String tinBhfPath) throws Exception {
        String sdcId = null;
        sdcId = new String(Files.readAllBytes(
                Paths.get(SkmmUtil.getEbmDevicePath(tinBhfPath) + File.separator + "sdcId", new String[0])));
        sdcId = AesCoder.decrypt(sdcId);
        return sdcId;
    }

    public static void setMrcNo(String mrcNo, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmDevicePath(tinBhfPath);
        mrcNo = AesCoder.encrypt(mrcNo);
        Files.write(Paths.get(filePath + File.separator + "mrcNo", new String[0]), mrcNo.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static String getMrcNo(String tinBhfPath) throws Exception {
        String mrcNo = null;
        mrcNo = new String(Files.readAllBytes(
                Paths.get(SkmmUtil.getEbmDevicePath(tinBhfPath) + File.separator + "mrcNo", new String[0])));
        mrcNo = AesCoder.decrypt(mrcNo);
        return mrcNo;
    }

    public static void setDevSerNo(String dvcSrlNo, String tinBhfPath) throws Exception {
        String filePath = SkmmUtil.getEbmDevicePath(tinBhfPath);
        dvcSrlNo = AesCoder.encrypt(dvcSrlNo);
        Files.write(Paths.get(filePath + File.separator + "dvcSrlNo", new String[0]), dvcSrlNo.getBytes(),
                new java.nio.file.OpenOption[0]);
    }

    public static String getDevSerNo(String tinBhfPath) throws Exception {
        String dvcSrlNo = null;
        dvcSrlNo = new String(Files.readAllBytes(
                Paths.get(SkmmUtil.getEbmDevicePath(tinBhfPath) + File.separator + "dvcSrlNo", new String[0])));
        dvcSrlNo = AesCoder.decrypt(dvcSrlNo);
        return dvcSrlNo;
    }
}
