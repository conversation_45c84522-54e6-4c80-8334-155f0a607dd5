package com.struts.etims.config.client;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.net.URI;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.struts.etims.config.manager.DeviceManager;
import com.struts.etims.config.util.PropertiesConfig;
import com.struts.etims.config.util.VsdcUtil;
import com.struts.etims.execute.model.InitInfoReq;
import com.struts.etims.manage.DataHistoryManager;

@Component
public class ApiClient {
    private static final Logger logger = LoggerFactory.getLogger(ApiClient.class);

    @Autowired
    PropertiesConfig propConfig;

    @Autowired
    DataHistoryManager histMngr;

    private String getRequestUrl(String requestURI) throws Exception {
        String serverUrl = this.propConfig.getServerUrl();
        return serverUrl + "/" + requestURI;
    }

    public String getClient(ApiClientArg arg, Object reqObj) throws Exception {
        return getClient(arg, reqObj, true);
    }

    public String getClient(ApiClientArg arg, Object reqObj, boolean header) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost postMethod = null;
        String reqUrl = null;
        Object reqBodyObj = null;
        String reqBodyStr = null;
        String resStr = "";
        String resExCd = null;
        String resExMsg = null;
        StringBuilder tinBhfPath = new StringBuilder();

        try {
            reqUrl = getRequestUrl(arg.getUri());
            logger.info("reqUrl=" + reqUrl);
            postMethod = new HttpPost(reqUrl);
            postMethod.addHeader("Content-Type", "application/json");
        } catch (Exception ex) {
            resExCd = "891";
            resExMsg = "An error occurred while Request URL is created.";
        }

        logger.info("resExCd=" + resExCd);
        // logger.info("reqObj=" + reqObj);
        logger.info("header=" + header);

        if (resExCd == null &&
                reqObj != null &&
                header) {
            try {
                Class<?> reqCls = reqObj.getClass();
                Field[] reqFields = reqCls.getDeclaredFields();
                Object value = null;
                for (Field reqField : reqFields) {
                    if ("tin".equals(reqField.getName()) || "bhfId".equals(reqField.getName())) {
                        reqField.setAccessible(true);
                        value = "";
                        if (reqField.get(reqObj) != null)
                            value = reqField.get(reqObj);
                        if ("tin".equals(reqField.getName())) {
                            tinBhfPath.append(value + "_");
                        } else {
                            tinBhfPath.append(value);
                        }
                        postMethod.setHeader(reqField.getName(), (String) value);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                resExCd = "892";
                resExMsg = "An error occurred while Request Header data is created.";
            }

            String cmdKey = DeviceManager.getKey("cmcKey",
                    tinBhfPath.toString());

            logger.info("cmcKey=" + cmdKey);
            postMethod.setHeader("cmcKey", cmdKey);

            logger.info("tinBhfPath.toString()=" + tinBhfPath.toString());
        }

        InitInfoReq req = new InitInfoReq();
        String branchID = "";
        String tin = "";
        String deviceSerial = "";

        if (reqUrl.equals("https://etims-api.kra.go.ke/etims-api/selectInitVsdcInfo")) {
            Class<?> reqCls = reqObj.getClass();
            Field[] reqFields = reqCls.getDeclaredFields();
            for (Field reqField : reqFields) {
                String requestFieldName = reqField.getName();
                // logger.info("requestFieldName=" + requestFieldName);

                reqField.setAccessible(true);
                String requestFieldValue = (String) reqField.get(reqObj);
                if (reqField.get(reqObj) != null) {
                    // logger.info("requestFieldValue=" + requestFieldValue);

                    switch (requestFieldName) {
                        case "tin":
                            tin = requestFieldValue;
                            break;

                        case "bhfId":
                            branchID = requestFieldValue;
                            break;

                        case "dvcSrlNo":
                            deviceSerial = requestFieldValue;
                            break;

                    }
                }
            }

            req.setBhfId(branchID);
            req.setTin(tin);
            req.setDvcSrlNo(deviceSerial);
        }

        // logger.info("resExCd=" + resExCd);
        // logger.info("arg.getReqBodyCls()=" + arg.getReqBodyCls());
        // logger.info("reqObj=" + reqObj.toString());

        if (resExCd == null &&
                arg.getReqBodyCls() != null)
            try {
                reqBodyObj = arg.getReqBodyCls();
                VsdcUtil.copyBean(reqBodyObj, reqObj);
                reqBodyStr = VsdcUtil.objectToJson(reqBodyObj);
                // logger.info("reqBodyStr=" + reqBodyStr.toString());
                ObjectMapper objectMapper = new ObjectMapper();
                String jsonInputString = objectMapper.writeValueAsString(reqObj);
                logger.info("jsonInputString=" + jsonInputString);
                postMethod.setEntity((HttpEntity) new StringEntity(jsonInputString, ContentType.APPLICATION_JSON));
            } catch (Exception ex) {
                ex.printStackTrace();
                resExCd = "893";
                resExMsg = "An error occurred while Request Body data is created.";
            }

        // logger.info("resExCd=" + resExCd);
        // logger.info(httpClient);

        if (resExCd == null)
            try {
                CloseableHttpResponse response = httpClient.execute((HttpUriRequest) postMethod);
                int httpStatus = response.getStatusLine().getStatusCode();
                logger.info("httpStatus=" + httpStatus);
                if (httpStatus == 200) {
                    HttpEntity entity = response.getEntity();
                    resStr = EntityUtils.toString(entity, "UTF-8");
                    logger.info("resStr=" + resStr);
                    EntityUtils.consume(entity);
                } else {
                    resExCd = "896";
                    resExMsg = "An error regarding Request Status occurred. : " + httpStatus;
                }
            } catch (Exception ex) {
                resExCd = "894";
                resExMsg = "Connection to KRA API refused Check Connection.";
            } finally {
                if (postMethod != null)
                    postMethod.releaseConnection();
                if (httpClient != null)
                    httpClient.close();
            }
        if (resExCd != null)
            throw new ApiException(resExCd, resExMsg);
        return resStr;
    }

    public String getClient(String reqUrl, Object reqBody) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        BufferedReader resBufReader = null;
        String resStr = "";
        HttpGet getMethod = new HttpGet(getRequestUrl(reqUrl));
        getMethod.addHeader("Content-Type", "application/json");
        if (reqBody != null) {
            List<NameValuePair> params = new ArrayList<>();
            Field[] reqBodyFields = reqBody.getClass().getDeclaredFields();
            for (Field reqBodyField : reqBodyFields) {
                reqBodyField.setAccessible(true);
                Object value = reqBodyField.get(reqBody);
                if (value != null)
                    params.add(new BasicNameValuePair(reqBodyField.getName(), value.toString()));
            }
            String paramString = EntityUtils
                    .toString((HttpEntity) new UrlEncodedFormEntity(params, Charset.forName("UTF-8")));
            getMethod.setURI(new URI(getMethod.getURI().toString() + "?" + paramString));
        }
        InputStreamReader resStream = null;
        try {
            CloseableHttpResponse response = httpClient.execute((HttpUriRequest) getMethod);
            int httpStatus = response.getStatusLine().getStatusCode();
            if (httpStatus == 200) {
                HttpEntity entity = response.getEntity();
                resStr = EntityUtils.toString(entity, "UTF-8");
                EntityUtils.consume(entity);
            } else {
                resStr = "";
            }
        } catch (Exception exception) {

        } finally {
            if (getMethod != null)
                getMethod.releaseConnection();
            if (httpClient != null)
                httpClient.close();
        }
        return resStr;
    }
}
