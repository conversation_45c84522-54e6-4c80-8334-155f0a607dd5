package com.struts.etims.model;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import java.util.HashMap;
import java.util.Map;

public class BTCExchangeRates {
    private Map<String, BTCExchangeRate> rates = new HashMap<>();

    public Map<String, BTCExchangeRate> getRates() {
        return rates;
    }

    @JsonAnySetter
    public void setRates(String currencyCode, BTCExchangeRate rate) {
        this.rates.put(currencyCode, rate);
    }
}
