package com.struts.etims.repo;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import com.struts.etims.entity.Client;

public interface ClientRepository extends JpaRepository<Client, Long> {

    List<Client> findAllByIdIn(List<Long> clientIds);

    @Query("SELECT c FROM Client c WHERE c.deletedAt IS NULL ORDER BY id DESC")
    List<Client> findAllActiveClients();

    Client findByName(String name);

    @Query("SELECT COUNT(*) FROM Client c WHERE c.deletedAt IS NULL")
    Long countActiveClients();
}
