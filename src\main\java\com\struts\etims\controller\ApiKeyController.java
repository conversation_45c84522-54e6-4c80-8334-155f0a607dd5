package com.struts.etims.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.entity.ClientApiKey;
import com.struts.etims.model.ClientApiKeyList;
import com.struts.etims.service.ClientApiKeyService;

@RestController
@RequestMapping("/api-keys")
public class ApiKeyController {

    @Autowired
    ClientApiKeyService clientApiKeyService;

    @PostMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public ClientApiKey createClientApiKey(@RequestBody ClientApiKey clientApiKey) {
        return clientApiKeyService.save(clientApiKey);
    }

    @GetMapping("")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public ClientApiKeyList filterClientApiKeys() {
        return clientApiKeyService.filterClientApiKeys();
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public ClientApiKey deleteClientApiKey(@PathVariable Long id) {
        return clientApiKeyService.deleteClientApiKey(id);
    }
}
