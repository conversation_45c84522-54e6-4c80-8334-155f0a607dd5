package com.struts.etims.execute.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrnsPurchaseSaveItem {
    private Integer itemSeq;

    private String itemCd;

    private String itemClsCd;

    private String itemNm;

    private String bcd;

    private String spplrItemClsCd;

    private String spplrItemCd;

    private String spplrItemNm;

    private String pkgUnitCd;

    private BigDecimal pkg;

    private String qtyUnitCd;

    private BigDecimal qty;

    private BigDecimal prc;

    private BigDecimal splyAmt;

    private BigDecimal dcRt;

    private BigDecimal dcAmt;

    private BigDecimal taxblAmt;

    private String taxTyCd;

    private BigDecimal taxAmt;

    private BigDecimal totAmt;

    private String itemExprDt;
}
