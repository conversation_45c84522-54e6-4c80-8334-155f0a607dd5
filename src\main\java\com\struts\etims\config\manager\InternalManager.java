package com.struts.etims.config.manager;

import com.struts.etims.config.util.AesEnc;
import com.struts.etims.config.util.Base32;
import com.struts.etims.config.util.Base32ToHex;
import com.struts.etims.config.util.SkmmUtil;

public class InternalManager {
    public String getInternalData(String salesTyCd, String rcptTyCd, double taxTyBTaxAmt, long rptNo,
            long totInvcNoCnt, String tinBhfPath) throws Exception {
        String rstData = "";
        int intNSTaxAmtB = 0;
        int intNRTaxAmtB = 0;
        if ("N".equals(salesTyCd))
            if ("S".equals(rcptTyCd)) {
                intNSTaxAmtB = (int) taxTyBTaxAmt;
            } else {
                intNRTaxAmtB = (int) taxTyBTaxAmt;
            }
        int intTaxTyBTaxAmt = (int) Math.floor(taxTyBTaxAmt);
        intNRTaxAmtB += intTaxTyBTaxAmt;
        StringBuffer sb = new StringBuffer();
        sb.append(AesEnc.longToHex(intNSTaxAmtB, 5));
        sb.append(AesEnc.longToHex(intNRTaxAmtB, 5));
        sb.append(AesEnc.longToHex(rptNo, 2));
        sb.append(AesEnc.longToHex(totInvcNoCnt, 4));

        String intrlKey = DeviceManager.getKey("intrlKey", tinBhfPath);
        System.out.println("intrlKey: " + intrlKey);

        Base32ToHex base32 = new Base32ToHex();
        AesEnc internal = new AesEnc();
        byte[] base32DecodedData = base32.decode(intrlKey);

        // System.out.println("base32DecodedData: " + base32DecodedData);

        String hexKey = base32.convertToHex(base32DecodedData).toUpperCase();

        System.out.println("hexKey: " + hexKey);
        System.out.println("sbString=" + sb.toString());

        rstData = internal.encrypt(sb.toString(), hexKey);
        // System.out.println("rstData: " + rstData);
        return rstData;
    }

    public String getSignature(String rcptDt, String tin, String custTin, long invcNo, double taxTyBTaxblAmt,
            double taxTyBTaxAmt, double taxTyATaxblAmt, double taxTyATaxAmt, String salesTyCd, String rcptTyCd,
            long totInvcNoCnt, long salesTyTotInvcNoCnt, String tinBhfPath) throws Exception {

        String rstData = "";
        String mrcNo = DeviceManager.getMrcNo(tinBhfPath);
        String sdcId = DeviceManager.getSdcID(tinBhfPath);
        String signKey = DeviceManager.getKey("signKey", tinBhfPath);

        StringBuffer sb = new StringBuffer();
        sb.append(rcptDt);
        sb.append(tin);
        sb.append(custTin);
        sb.append(mrcNo);
        sb.append(SkmmUtil.lpad(String.valueOf(invcNo), 10));
        sb.append(" 0,00");
        sb.append(SkmmUtil.lpadAmount(String.format("%.2f", new Object[] { Double.valueOf(taxTyATaxblAmt) }), 15));
        sb.append(SkmmUtil.lpadAmount(String.format("%.2f", new Object[] { Double.valueOf(taxTyATaxAmt) }), 15));
        sb.append("18,00");
        sb.append(SkmmUtil.lpadAmount(String.format("%.2f", new Object[] { Double.valueOf(taxTyBTaxblAmt) }), 15));
        sb.append(SkmmUtil.lpadAmount(String.format("%.2f", new Object[] { Double.valueOf(taxTyBTaxAmt) }), 15));
        sb.append(" 0,00");
        sb.append(SkmmUtil.lpadAmount("0,00", 15));
        sb.append(SkmmUtil.lpadAmount("0,00", 15));
        sb.append(" 0,00");
        sb.append(SkmmUtil.lpadAmount("0,00", 15));
        sb.append(SkmmUtil.lpadAmount("0,00", 15));
        sb.append(salesTyCd);
        sb.append(rcptTyCd);
        sb.append(sdcId);
        sb.append(rcptDt);
        sb.append(SkmmUtil.lpad(String.valueOf(totInvcNoCnt), 10));
        sb.append(SkmmUtil.lpad(String.valueOf(salesTyTotInvcNoCnt), 10));

        Base32 b = new Base32();

        // System.out.println(sb.toString());
        // System.out.println("leng:" + sb.toString().length());

        // System.out.println("signKey: " + signKey);
        rstData = b.hmacSha1(sb.toString(), signKey);
        // System.out.println("rstData: " + rstData);

        return rstData;
    }
}
