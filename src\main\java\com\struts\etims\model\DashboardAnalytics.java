package com.struts.etims.model;

import java.util.List;

import com.struts.etims.entity.Client;
import com.struts.etims.entity.License;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class DashboardAnalytics {

    private Long clientsCount;
    private Long usersCount;
    private Long licensesCount;
    private Long licenesNearingExpiryCount;
    private Long expiredLicensesCount;
    private List<Client> clientExpiryList;
    private List<License> expiringLicenses;
}