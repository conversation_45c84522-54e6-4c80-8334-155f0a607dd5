APP_VERSION := 1.1.29

compile: 
	mvn clean package

deploy-prod:
	mvn clean package
	scp .\target\struts-etims-vscu-$(APP_VERSION).jar gidemn@139.162.207.36:/home/<USER>/Apps/ETIMS_VSCU


deploy-rangechem:
	mvn clean package
	scp -P 38467 .\target\struts-etims-vscu-$(APP_VERSION).jar rangeadmin@197.232.21.69:/home/<USER>/Apps/ETIMS_VSCU	


deploy-hash:
	mvn clean package
	scp .\target\struts-etims-vscu-$(APP_VERSION).jar hash@178.79.173.126:/home/<USER>/Apps/ETIMS_VSCU

deploy-sandbox:
	mvn clean package
	scp .\target\struts-etims-vscu-$(APP_VERSION).jar hash@178.79.173.126:/home/<USER>/Apps/ETIMS_VSCU


down: 
	docker-compose down

test: 
	# mvn test
	echo "running unit tests..."

package: 
	mvn clean package

server: 
	mvn spring-boot:run -Dspring-boot.run.profiles=dev
	
up: 
	docker-compose -f docker-compose.yml up --build --detach	

macos: 
	docker-compose -f docker-compose-macos.yml up --build --detach	
	
