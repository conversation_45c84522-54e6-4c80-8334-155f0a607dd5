package com.struts.etims.worker;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.struts.etims.config.manager.DeviceManager;
import com.struts.etims.config.manager.InternalManager;
import com.struts.etims.service.ETIMSService;

@Component
@PropertySource({ "classpath:application.properties" })
public class EtimsWorker {
    // private static final Logger logger =
    // LoggerFactory.getLogger(EtimsWorker.class);

    // private static final SimpleDateFormat dateFormat = new
    // SimpleDateFormat("h:mm:ss a");

    @Autowired
    private ETIMSService etimsService;

    @Value("${can.post.data.to.kra}")
    private Boolean canPostDataToKRA;

    @Scheduled(fixedRate = (1000 * 1)) // runs every 1 second
    @Async
    public void UploadSalesTransactionsToKRA() {

        if (canPostDataToKRA) {
            // logger.info("Etims Sender Worker Current time is :: {} ",
            // dateFormat.format(new Date()));

            // Post any unsent signing data to KRA every 5 seconds
            etimsService.PostUnsentSalesTransactionsToKRA();
        }
    }

    @Scheduled(fixedRate = (4000 * 1)) // runs every 4 seconds
    @Async
    public void ProcessFailedSalesTransactions() {

        if (canPostDataToKRA) {
            // logger.info("Etims Retry Transactions Worker Current time is :: {} ",
            // dateFormat.format(new Date()));

            // Post any failed signing trx with error 910 to KRA every 10 seconds
            etimsService.RetryFailedSalesTransactionsSendingToKRA();
        }
    }

    // Test retrieval and logging of VSCU Keys
    public void getKeysTest() {
        InternalManager internalMngr = new InternalManager();

        // getKeys
        try {
            DeviceManager.getKeys("P051922564N_00");
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            String tinBhfPath = "P051922564N_00";
            String intrlData = internalMngr.getInternalData("N", "S",
                    0.00, 376, 20146, tinBhfPath);

            // System.out.println("intrlData: " + intrlData);

            // String signData = internalMngr.getSignature("20240814013856",
            // "P051922564N",
            // "", 20146,
            // 2120.00,
            // 292.41, 0.00,
            // 0.00, "N", "S",
            // 20146, 20146, tinBhfPath);

            // System.out.println("signData: " + signData);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
