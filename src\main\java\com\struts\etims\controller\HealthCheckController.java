package com.struts.etims.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.struts.etims.model.HealthCheck;

@RestController
@RequestMapping("/health-check")
public class HealthCheckController {

    @GetMapping("")
    public HealthCheck healthCheckBlank() {
        return performHealthCheck();
    }

    @GetMapping("/")
    public HealthCheck healthCheck() {
        return performHealthCheck();
    }

    public HealthCheck performHealthCheck() {
        HealthCheck healthCheck = new HealthCheck();
        healthCheck.setStatus("Ok");
        healthCheck.setDescription("All services up.");
        return healthCheck;
    }
}
