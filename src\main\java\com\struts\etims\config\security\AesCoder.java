package com.struts.etims.config.security;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.security.Key;
import java.security.KeyStore;
import java.security.KeyStore.PasswordProtection;
import java.security.KeyStore.SecretKeyEntry;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

@Component
public class AesCoder {
    private static final Logger logger = LoggerFactory.getLogger(AesCoder.class);

    private static ResourceLoader resourceLoader;

    private static final String ENCODE = "UTF-8";

    @Autowired
    public AesCoder(ResourceLoader resourceLoader) {
        AesCoder.resourceLoader = resourceLoader;
    }

    public static String encrypt(String inputStr) throws Exception {
        String result = null;
        if (inputStr == null || inputStr.length() < 1)
            return result;
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(1, getAESKey());
        byte[] byteValue = cipher.doFinal(inputStr.getBytes("UTF-8"));
        Base64 base64EnDe = new Base64();
        result = base64EnDe.encodeToString(byteValue).replaceAll("\r\n", "");
        return result;
    }

    public static String decrypt(String encStr) throws Exception {
        String result = "";
        if (encStr == null || encStr.length() < 1)
            return result;
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        Key aesKey = getAESKey();
        cipher.init(2, aesKey);
        Base64 base64EnDe = new Base64();
        byte[] origianl = cipher.doFinal(base64EnDe.decode(encStr));
        result = new String(origianl, "UTF-8");
        return result;
    }

    private static Key getAESKey() throws Exception {
        String keystorePwd = "rra123";
        String alias = "MyAESKey";
        String keyPwd = "rra123";
        try {
            Resource resource = resourceLoader.getResource("classpath:/mykeystore");
            InputStream keyStore = resource.getInputStream();
            KeyStore keystore = KeyStore.getInstance("JCEKS");
            keystore.load(keyStore, keystorePwd.toCharArray());
            Key key = keystore.getKey(alias, keyPwd.toCharArray());

            SecretKey secretKey = (SecretKey) keystore.getKey(alias, keyPwd.toCharArray());

            // Base64 base64EnDe = new Base64();
            // String result =
            // base64EnDe.encodeToString(secretKey.getEncoded()).replaceAll("\r\n", "");
            // System.out.println("secret key=" + secretKey.getEncoded());
            // System.out.println("base64 encoded secret key=" + result);

            return key;
        } catch (IOException e) {
            logger.error("Failed to load keystore or read from the input stream.", e);
            throw e;
        } catch (KeyStoreException e) {
            logger.error("Failed to get an instance of KeyStore.", e);
            throw e;
        } catch (NoSuchAlgorithmException e) {
            logger.error("Failed to load the keystore due to a missing algorithm.", e);
            throw e;
        } catch (CertificateException e) {
            logger.error("Failed to load the keystore due to a certificate problem.", e);
            throw e;
        }
    }
}