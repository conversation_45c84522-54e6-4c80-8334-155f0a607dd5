package com.struts.etims.config.util;

import java.security.Key;
import java.util.Arrays;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class AesEnc {
    private static final String ALGO = "AES";

    private static final String base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";

    Base32ToHex base32 = new Base32ToHex();

    public static final String longToHex(long lval, int nbByte) {
        String hexString = null;
        hexString = Long.toHexString(lval);
        int nbChar = hexString.length();
        if (nbChar < nbByte * 2)
            for (int i = nbChar + 1; i <= nbByte * 2; i++)
                hexString = "0" + hexString;
        return hexString;
    }

    private final byte[] HexToByteArray(String hex, int n) {
        int numberChars = hex.length();
        // System.out.println("numberChars=" + numberChars);
        if (numberChars % 2 != 0)
            hex = "0" + hex;
        byte[] bytes = new byte[n];
        int j = 0;
        int i;
        for (i = 0; i <= numberChars - 1; i += 2) {
            bytes[j] = (byte) Integer.parseInt(hex.substring(i, i + 2), 16);
            j++;
        }
        return bytes;
    }

    public String encrypt(String data, String key1) throws Exception {
        // System.out.println("data=" + data + ", key=" + key1);
        Key key = generateKey(key1);
        Cipher c = Cipher.getInstance("AES/ECB/NoPadding");
        c.init(1, key);
        byte[] encVal = c.doFinal(HexToByteArray(data, 16));
        // System.out.println("-----------Signed bytes----------");
        // System.out.println(Arrays.toString(encVal));
        // System.out.println("-----------Signed bytes----------");
        String encryptedValue = encode(encVal);
        return encryptedValue;
    }

    private Key generateKey(String keys) throws Exception {
        Key key = new SecretKeySpec(HexToByteArray(keys, 32), "AES");
        return key;
    }

    public static String encode(byte[] bytes) {
        int i = 0, index = 0, digit = 0;
        StringBuffer base32 = new StringBuffer((bytes.length + 7) * 8 / 5);
        int[] unsignedArr = new int[bytes.length];
        while (i < bytes.length) {
            int currByte = (bytes[i] >= 0) ? bytes[i] : (bytes[i] + 256);
            unsignedArr[i] = currByte;
            if (index > 3) {
                int nextByte;
                if (i + 1 < bytes.length) {
                    nextByte = (bytes[i + 1] >= 0) ? bytes[i + 1] : (bytes[i + 1] + 256);
                } else {
                    nextByte = 0;
                }
                digit = currByte & 255 >> index;
                index = (index + 5) % 8;
                digit <<= index;
                digit |= nextByte >> 8 - index;
                i++;
            } else {
                digit = currByte >> 8 - index + 5 & 0x1F;
                index = (index + 5) % 8;
                if (index == 0)
                    i++;
            }
            // System.out.println("currByte=" + currByte + ", digit=" + digit);
            base32.append("ABCDEFGHIJKLMNOPQRSTUVWXYZ234567".charAt(digit));
        }

        // System.out.println(Arrays.toString(unsignedArr));
        return base32.toString();
    }
}
