package com.struts.etims.repo;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import com.struts.etims.entity.EtimsSaleItem;
import org.springframework.data.jpa.repository.Query;

public interface EtimsSaleItemRepository extends JpaRepository<EtimsSaleItem, Long> {

    @Query("SELECT i FROM EtimsSaleItem i WHERE i.etimsSaveSaleTransactionID = ?1 ORDER BY id ASC")
    List<EtimsSaleItem> findAllForSalesTransaction(Long etimsSaveSaleTransactionID);
}
