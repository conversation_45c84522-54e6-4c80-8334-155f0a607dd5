package com.struts.etims.repo;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.struts.etims.entity.License;

public interface LicenseRepository extends JpaRepository<License, Long> {
        // Define a custom query to retrieve licenses about to expire
        // @Query("SELECT l.id, l.license, l.expires_at, l.licenseDaysRemaining,
        // l.description, l.client_id, l.deleted_at, l.createdAt, l.updatedAt FROM
        // License l WHERE l.deleted_at IS NULL AND l.expires_at <= CURRENT_DATE + 30")
        @Query("SELECT l FROM License l, Client c WHERE l.client_id = c.id AND l.deletedAt IS NULL " +
                        "AND c.deletedAt IS NULL AND l.expires_at <= CURRENT_DATE + 30")
        List<License> findLicensesAboutToExpire();

        // Query to retrieve already expired licenses
        @Query("SELECT l FROM License l, Client c WHERE l.client_id = c.id AND l.deletedAt IS NULL " +
                        "AND c.deletedAt IS NULL AND l.expires_at < CURRENT_DATE")
        List<License> findExpiredLicenses();

        @Modifying
        @Transactional
        @Query("UPDATE License l SET l.deletedAt = CURRENT_TIMESTAMP WHERE l.client_id = :clientId AND l.deletedAt IS NULL")
        int updateDeletedAtByClientId(Long clientId);

        @Query("SELECT COUNT(*) FROM License l, Client c WHERE l.client_id = c.id AND c.deletedAt IS NULL AND l.deletedAt IS NULL AND l.expires_at > CURRENT_DATE")
        Long countValidLicences();
}
