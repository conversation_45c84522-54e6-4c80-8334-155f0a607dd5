package com.struts.etims.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.struts.etims.entity.ApplicationStatistic;
import com.struts.etims.entity.Client;
import com.struts.etims.entity.ClientApiKey;
import com.struts.etims.forms.ApplicationStatisticForm;
import com.struts.etims.model.ApplicationStatisticList;
import com.struts.etims.model.ApplicationStatisticResponse;
import com.struts.etims.model.Pagination;
import com.struts.etims.repo.ApplicationStatisticRepository;
import com.struts.etims.repo.ClientApiKeyRepository;
import com.struts.etims.repo.ClientRepository;

@Service
public class ApplicationStatisticService {

    @Autowired
    ClientApiKeyRepository clientApiKeyRepository;

    @Autowired
    ClientRepository clientRepository;

    @Autowired
    ApplicationStatisticRepository applicationStatisticRepository;

    public ApplicationStatisticList filterApplicationStatistics() {
        ApplicationStatisticList list = new ApplicationStatisticList();
        List<ApplicationStatistic> statistics = applicationStatisticRepository.findAll(Sort.by("id").descending());
        list.setApplicationStatistics(statistics);

        Pagination pagination = new Pagination(1, 20);
        Long count = applicationStatisticRepository.count();
        pagination.setCount(count);
        list.setPagination(pagination);
        return list;
    }

    public ApplicationStatisticResponse save(ApplicationStatisticForm form) {

        ApplicationStatisticResponse response = new ApplicationStatisticResponse();
        if (form.getApiKey() == null) {
            return response;
        }

        // Authenticate using client api key
        ClientApiKey apiKey = clientApiKeyRepository.findByApiKey(form.getApiKey());
        if (apiKey == null) {
            return response;
        }

        // Retrieve client details from api key
        Long clientID = apiKey.getClientId();
        if (clientID == null) {
            return response;
        }

        Client client = clientRepository.findById(clientID).get();
        if (client == null) {
            return response;
        }

        ApplicationStatistic statistic = new ApplicationStatistic();
        statistic.setClientId(client.getId());
        statistic.setClientApiKeyID(apiKey.getId());
        statistic.setCount(form.getCount());
        statistic.setStatName(form.getStatName());
        statistic.setDescription(form.getDescription());
        statistic.setDisplayValue(form.getDisplayValue());
        applicationStatisticRepository.save(statistic);

        response.setSuccess(true);
        response.setStatID(statistic.getId());

        return response;
    }
}
