package com.struts.etims.config.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseRes {
    private String resultCd;

    private String resultMsg;

    private String resultDt;

    public String getResultCd() {
        return this.resultCd;
    }

    public void setResultCd(String resultCd) {
        this.resultCd = resultCd;
    }

    public String getResultMsg() {
        return this.resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getResultDt() {
        return this.resultDt;
    }

    public void setResultDt(String resultDt) {
        this.resultDt = resultDt;
    }
}
