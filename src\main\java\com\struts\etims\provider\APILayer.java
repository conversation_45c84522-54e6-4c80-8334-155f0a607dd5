package com.struts.etims.provider;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.struts.etims.model.ApiLayerResponse;

@Service
public class APILayer {

    @Value("${apilayer.apikey}")
    private String apiKey;

    private final String apiUrl = "https://api.apilayer.com/currency_data/convert";

    private static final Logger logger = LoggerFactory.getLogger(APILayer.class);

    public ApiLayerResponse convertCurrency(String from, String to, double amount) {
        RestTemplate restTemplate = new RestTemplate();
        String url = apiUrl + "?from=" + from + "&to=" + to + "&amount=1";
        HttpHeaders headers = new HttpHeaders();
        headers.set("apikey", apiKey);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        logger.info("apilayer currency response = " + response);

        ApiLayerResponse apiLayerResponse = convertResponseToApiLayer(response.getBody());

        return apiLayerResponse;
    }

    private ApiLayerResponse convertResponseToApiLayer(String responseBody) {
        // Use Jackson ObjectMapper to deserialize the JSON string into CustomModel
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(responseBody, ApiLayerResponse.class);
        } catch (JsonProcessingException e) {
            // Handle deserialization exception
            e.printStackTrace();
            return null; // Or throw an exception based on your error handling strategy
        }
    }
}
