package com.struts.etims.execute.model;

import java.math.BigDecimal;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrnsSalesSaveWrReq {
    private String tin;

    private String bhfId;

    private Long invcNo;

    private Long orgInvcNo;

    private String custTin;

    private String custNm;

    private String salesTyCd;

    private String rcptTyCd;

    private String pmtTyCd;

    private String salesSttsCd;

    private String cfmDt;

    private String salesDt;

    private String stockRlsDt;

    private String cnclReqDt;

    private String cnclDt;

    private String rfdDt;

    private String rfdRsnCd;

    private Integer totItemCnt;

    private BigDecimal taxblAmtA;

    private BigDecimal taxblAmtB;

    private BigDecimal taxblAmtC;

    private BigDecimal taxblAmtD;

    private BigDecimal taxblAmtE;

    private BigDecimal taxRtA;

    private BigDecimal taxRtB;

    private BigDecimal taxRtC;

    private BigDecimal taxRtD;

    private BigDecimal taxRtE;

    private BigDecimal taxAmtA;

    private BigDecimal taxAmtB;

    private BigDecimal taxAmtC;

    private BigDecimal taxAmtD;

    private BigDecimal taxAmtE;

    private BigDecimal totTaxblAmt;

    private BigDecimal totTaxAmt;

    private BigDecimal totAmt;

    private String prchrAcptcYn;

    private String remark;

    private String regrId;

    private String regrNm;

    private String modrId;

    private String modrNm;

    private TrnsSalesSaveWrReceipt receipt;

    private List<TrnsSalesSaveWrItem> itemList;
}
