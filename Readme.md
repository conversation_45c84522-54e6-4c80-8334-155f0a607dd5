# About

This is the ETIMS VSCU Java Spring boot source code.

# Local data storage

AppData/EbmData

# Deploy

To deploy the application to production, following the following steps;

1. Update the software application version in the pom.xml file

2. Update the application version in the Makefile to the same version

3. Run the following make commmand to upload the compiled jar file to prod;

   make deploy-prod

4. The application will be get compiled and uploaded to the production server, you can then login to the server via cmd,
   stop the respective service - whether sandbox or prod, and startup the latest application via jar command;

   nohup java -jar application.jar &
