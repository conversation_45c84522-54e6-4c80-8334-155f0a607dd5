package com.struts.etims.execute.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportZSaveReq {
    private String tin;

    private String bhfId;

    private String rptDe;

    private String sdcId;

    private Long rptNo;

    private Integer rcptPbctCnt = Integer.valueOf(0);

    private Long rcptOpnNo = Long.valueOf(0L);

    private Long rcptClsNo = Long.valueOf(0L);

    private Integer nrmRcptPbctCnt = Integer.valueOf(0);

    private Long nrmRcptOpnNo = Long.valueOf(0L);

    private Long nrmRcptClsNo = Long.valueOf(0L);

    private BigDecimal nrmSalesAmt = new BigDecimal(0);

    private BigDecimal nrmRfdAmt = new BigDecimal(0);

    private BigDecimal nrmSalesTaxAmt = new BigDecimal(0);

    private BigDecimal nrmRfdTaxAmt = new BigDecimal(0);

    private Integer cpyRcptPbctCnt = Integer.valueOf(0);

    private Long cpyRcptOpnNo = Long.valueOf(0L);

    private Long cpyRcptClsNo = Long.valueOf(0L);

    private BigDecimal cpySalesAmt = new BigDecimal(0);

    private BigDecimal cpyRfdAmt = new BigDecimal(0);

    private BigDecimal cpySalesTaxAmt = new BigDecimal(0);

    private BigDecimal cpyRfdTaxAmt = new BigDecimal(0);

    private Integer trnRcptPbctCnt = Integer.valueOf(0);

    private Long trnRcptOpnNo = Long.valueOf(0L);

    private Long trnRcptClsNo = Long.valueOf(0L);

    private BigDecimal trnSalesAmt = new BigDecimal(0);

    private BigDecimal trnRfdAmt = new BigDecimal(0);

    private BigDecimal trnSalesTaxAmt = new BigDecimal(0);

    private BigDecimal trnRfdTaxAmt = new BigDecimal(0);

    private Integer pfmRcptPbctCnt = Integer.valueOf(0);

    private Long pfmRcptOpnNo = Long.valueOf(0L);

    private Long pfmRcptClsNo = Long.valueOf(0L);

    private BigDecimal pfmSalesAmt = new BigDecimal(0);

    private BigDecimal pfmRfdAmt = new BigDecimal(0);

    private BigDecimal pfmSalesTaxAmt = new BigDecimal(0);

    private BigDecimal pfmRfdTaxAmt = new BigDecimal(0);

    private String regrId;

    private String regrNm;
}
